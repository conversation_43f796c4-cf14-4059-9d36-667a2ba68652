/**
 * Validation Utilities
 * Provides validation functions for API requests
 */

import { body, query, param, validationResult } from 'express-validator'
import { errors } from '../middleware/errorHandler.js'
import { CONFIG } from '../config/server.js'
import {
  TASK_PRIORITY_VALUES,
  TASK_TYPE_VALUES,
  TASK_STATUS_VALUES,
  TASK_ESTIMATED_EFFORT_VALUES,
  TASK_ID_PATTERN,
  VALIDATION_CONSTRAINTS,
  API_CONFIG
} from '../../common/constants/index.js'
import { sanitizeData } from '../../common/validation/index.js'

/**
 * Handle validation errors
 * @param {Request} req - Express request object
 * @param {Response} res - Express response object
 * @param {Function} next - Next middleware function
 */
export function handleValidationErrors(req, res, next) {
  const errors_validation = validationResult(req)

  if (!errors_validation.isEmpty()) {
    const errorDetails = errors_validation.array().map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value
    }))

    return next(errors.VALIDATION_ERROR(errorDetails))
  }

  next()
}

/**
 * Common validation rules
 */
export const validators = {
  // Task validation rules
  taskId: param('task_id')
    .matches(/^[A-Z]{2,12}-[0-9]{1,4}(-[0-9]{1,3})?$/)
    .withMessage('Task ID must follow pattern: AAA-123 or AAA-123-1'),

  taskSummary: body('summary')
    .isString()
    .trim()
    .isLength({ min: VALIDATION_CONSTRAINTS.TASK_SUMMARY_MIN_LENGTH, max: VALIDATION_CONSTRAINTS.TASK_SUMMARY_MAX_LENGTH })
    .withMessage(`Summary must be between ${VALIDATION_CONSTRAINTS.TASK_SUMMARY_MIN_LENGTH} and ${VALIDATION_CONSTRAINTS.TASK_SUMMARY_MAX_LENGTH} characters`),

  taskDescription: body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: VALIDATION_CONSTRAINTS.TASK_DESCRIPTION_MAX_LENGTH })
    .withMessage(`Description must be less than ${VALIDATION_CONSTRAINTS.TASK_DESCRIPTION_MAX_LENGTH} characters`),

  taskPriority: body('priority')
    .optional()
    .isIn(TASK_PRIORITY_VALUES)
    .withMessage(`Priority must be: ${TASK_PRIORITY_VALUES.join(', ')}`),

  taskType: body('type')
    .optional()
    .isIn(TASK_TYPE_VALUES)
    .withMessage(`Type must be: ${TASK_TYPE_VALUES.join(', ')}`),

  taskStatus: body('status')
    .optional()
    .isIn(TASK_STATUS_VALUES)
    .withMessage(`Status must be: ${TASK_STATUS_VALUES.join(', ')}`),

  taskEstimatedEffort: body('estimated_effort')
    .optional()
    .isIn(TASK_ESTIMATED_EFFORT_VALUES)
    .withMessage(`Estimated effort must be: ${TASK_ESTIMATED_EFFORT_VALUES.join(', ')}`),

  taskEpic: body('epic')
    .optional()
    .isString()
    .trim()
    .isLength({ max: VALIDATION_CONSTRAINTS.TASK_EPIC_MAX_LENGTH })
    .withMessage(`Epic must be less than ${VALIDATION_CONSTRAINTS.TASK_EPIC_MAX_LENGTH} characters`),

  taskAssignedTo: body('assigned_to')
    .optional()
    .isString()
    .trim()
    .isLength({ max: VALIDATION_CONSTRAINTS.TASK_ASSIGNED_TO_MAX_LENGTH })
    .withMessage(`Assigned to must be less than ${VALIDATION_CONSTRAINTS.TASK_ASSIGNED_TO_MAX_LENGTH} characters`),

  projectId: body('project_id')
    .isString()
    .trim()
    .notEmpty()
    .withMessage('Project ID is required'),

  // Project validation rules
  projectIdParam: param('project_id')
    .isString()
    .trim()
    .notEmpty()
    .withMessage('Project ID is required'),

  projectName: body('name')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Project name must be between 1 and 100 characters'),

  projectDescription: body('description')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Project description must be less than 1000 characters'),

  // Query parameter validation
  limitQuery: query('limit')
    .optional()
    .isInt({ min: 1, max: CONFIG.PAGINATION_MAX_LIMIT })
    .withMessage(`Limit must be between 1 and ${CONFIG.PAGINATION_MAX_LIMIT}`),

  offsetQuery: query('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Offset must be a non-negative integer'),

  searchQuery: query('search')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Search query must be between 1 and 200 characters'),

  statusQuery: query('status')
    .optional()
    .isIn(['Backlog', 'In Progress', 'Done', 'Blocked'])
    .withMessage('Status must be: Backlog, In Progress, Done, or Blocked'),

  priorityQuery: query('priority')
    .optional()
    .isIn(['Low', 'Medium', 'High'])
    .withMessage('Priority must be: Low, Medium, or High'),

  typeQuery: query('type')
    .optional()
    .isIn(['Task', 'Story', 'Epic', 'Bug'])
    .withMessage('Type must be: Task, Story, Epic, or Bug'),

  // Bulk operations validation
  bulkTaskIds: body('task_ids')
    .isArray({ min: 1, max: 100 })
    .withMessage('Task IDs must be an array with 1-100 items')
    .custom((value) => {
      for (const taskId of value) {
        if (!taskId.match(/^[A-Z]{2,4}-[0-9]{1,4}(-[0-9]{1,2})?$/)) {
          throw new Error(`Invalid task ID format: ${taskId}`)
        }
      }
      return true
    }),

  bulkUpdates: body('updates')
    .isArray({ min: 1, max: 100 })
    .withMessage('Updates must be an array with 1-100 items')
    .custom((value) => {
      for (const update of value) {
        if (!update.task_id || typeof update.task_id !== 'string') {
          throw new Error('Each update must have a valid task_id')
        }
        if (!update.updates || typeof update.updates !== 'object') {
          throw new Error('Each update must have an updates object')
        }
      }
      return true
    })
}

/**
 * Validation rule sets for different endpoints
 */
export const validationRules = {
  // Task endpoints
  createTask: [
    validators.taskSummary,
    validators.projectId,
    validators.taskDescription,
    validators.taskPriority,
    validators.taskType,
    validators.taskStatus,
    validators.taskEstimatedEffort,
    validators.taskEpic,
    validators.taskAssignedTo,
    handleValidationErrors
  ],

  updateTask: [
    validators.taskId,
    validators.taskSummary.optional(),
    validators.taskDescription,
    validators.taskPriority,
    validators.taskType,
    validators.taskStatus,
    validators.taskEstimatedEffort,
    validators.taskEpic,
    validators.taskAssignedTo,
    handleValidationErrors
  ],

  updateTaskStatus: [
    validators.taskId,
    body('status')
      .isIn(['Backlog', 'In Progress', 'Done', 'Blocked'])
      .withMessage('Status must be: Backlog, In Progress, Done, or Blocked'),
    handleValidationErrors
  ],

  getTask: [
    validators.taskId,
    handleValidationErrors
  ],

  getTasks: [
    validators.limitQuery,
    validators.offsetQuery,
    validators.searchQuery,
    validators.statusQuery,
    validators.priorityQuery,
    validators.typeQuery,
    query('project_id').optional().isString().trim(),
    query('epic').optional().isString().trim(),
    query('assigned_to').optional().isString().trim(),
    handleValidationErrors
  ],

  deleteTask: [
    validators.taskId,
    handleValidationErrors
  ],

  bulkImportTasks: [
    body('tasks')
      .isArray({ min: 1 })
      .withMessage('Tasks must be a non-empty array'),
    body('tasks.*.task_id')
      .optional()
      .matches(TASK_ID_PATTERN)
      .withMessage('Task ID must follow pattern: AAA-123 or AAA-123-1'),
    body('tasks.*.summary')
      .isString()
      .trim()
      .isLength({ min: 1, max: VALIDATION_CONSTRAINTS.TASK_SUMMARY_MAX_LENGTH })
      .withMessage(`Summary is required and must be 1-${VALIDATION_CONSTRAINTS.TASK_SUMMARY_MAX_LENGTH} characters`),
    body('tasks.*.description')
      .optional()
      .isString()
      .trim()
      .isLength({ max: VALIDATION_CONSTRAINTS.TASK_DESCRIPTION_MAX_LENGTH })
      .withMessage(`Description must be less than ${VALIDATION_CONSTRAINTS.TASK_DESCRIPTION_MAX_LENGTH} characters`),
    body('tasks.*.priority')
      .optional()
      .isIn(TASK_PRIORITY_VALUES)
      .withMessage(`Priority must be one of: ${TASK_PRIORITY_VALUES.join(', ')}`),
    body('tasks.*.type')
      .optional()
      .isIn(TASK_TYPE_VALUES)
      .withMessage(`Type must be one of: ${TASK_TYPE_VALUES.join(', ')}`),
    body('tasks.*.status')
      .optional()
      .isIn(TASK_STATUS_VALUES)
      .withMessage(`Status must be one of: ${TASK_STATUS_VALUES.join(', ')}`),
    body('project_id')
      .isString()
      .trim()
      .isLength({ min: 1 })
      .withMessage('Project ID is required'),
    handleValidationErrors
  ],

  clearAllTasks: [
    body('confirm')
      .isBoolean()
      .equals(true)
      .withMessage('Confirmation is required to clear all tasks'),
    handleValidationErrors
  ],

  // Project endpoints
  createProject: [
    validators.projectName,
    validators.projectDescription,
    handleValidationErrors
  ],

  updateProject: [
    validators.projectIdParam,
    validators.projectName.optional(),
    validators.projectDescription,
    handleValidationErrors
  ],

  getProject: [
    validators.projectIdParam,
    handleValidationErrors
  ],

  deleteProject: [
    validators.projectIdParam,
    handleValidationErrors
  ],

  getProjectTasks: [
    validators.projectIdParam,
    validators.limitQuery,
    validators.offsetQuery,
    validators.statusQuery,
    validators.priorityQuery,
    validators.typeQuery,
    handleValidationErrors
  ],

  // Bulk operations
  bulkImportTasks: [
    validators.projectId,
    body('tasks')
      .isArray({ min: 1, max: 1000 })
      .withMessage('Tasks must be an array with 1-1000 items'),
    handleValidationErrors
  ],

  bulkUpdateTasks: [
    validators.bulkUpdates,
    handleValidationErrors
  ],

  bulkDeleteTasks: [
    validators.bulkTaskIds,
    handleValidationErrors
  ],

  // Search endpoints
  searchTasks: [
    query('q')
      .isString()
      .trim()
      .isLength({ min: 1, max: 200 })
      .withMessage('Search query is required and must be between 1 and 200 characters'),
    validators.limitQuery,
    validators.offsetQuery,
    validators.statusQuery,
    validators.priorityQuery,
    validators.typeQuery,
    query('project_id').optional().isString().trim(),
    handleValidationErrors
  ]
}



/**
 * Validate task ID format
 * @param {string} taskId - Task ID to validate
 * @returns {boolean} True if valid
 */
export function isValidTaskId(taskId) {
  return /^[A-Z]{2,4}-[0-9]{1,4}(-[0-9]{1,2})?$/.test(taskId)
}

/**
 * Validate UUID format
 * @param {string} uuid - UUID to validate
 * @returns {boolean} True if valid
 */
export function isValidUUID(uuid) {
  return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(uuid)
}

/**
 * Authentication validation rules
 */
export const authValidators = {
  // Email validation
  email: body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Must be a valid email address'),

  // Password validation with complexity requirements
  password: body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/(?=.*[a-z])/)
    .withMessage('Password must contain at least one lowercase letter')
    .matches(/(?=.*[A-Z])/)
    .withMessage('Password must contain at least one uppercase letter')
    .matches(/(?=.*\d)/)
    .withMessage('Password must contain at least one number'),

  // Simple password validation (for login)
  simplePassword: body('password')
    .isLength({ min: 1 })
    .withMessage('Password is required'),

  // Password confirmation validation
  passwordConfirm: body('passwordConfirm')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Password confirmation does not match password')
      }
      return true
    }),

  // Name validation
  name: body('name')
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('Name can only contain letters, spaces, hyphens, and apostrophes'),

  // Optional name validation (for profile updates)
  optionalName: body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s'-]+$/)
    .withMessage('Name can only contain letters, spaces, hyphens, and apostrophes'),

  // Optional email validation (for profile updates)
  optionalEmail: body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Must be a valid email address'),

  // Remember me validation
  rememberMe: body('rememberMe')
    .optional()
    .isBoolean()
    .withMessage('Remember me must be a boolean value'),

  // Token validation
  token: body('token')
    .isLength({ min: 1 })
    .withMessage('Token is required'),

  // Refresh token validation
  refreshToken: body('refreshToken')
    .isLength({ min: 1 })
    .withMessage('Refresh token is required'),

  // Current password validation (for password change)
  currentPassword: body('currentPassword')
    .isLength({ min: 1 })
    .withMessage('Current password is required'),

  // New password validation (for password change)
  newPassword: body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/(?=.*[a-z])/)
    .withMessage('New password must contain at least one lowercase letter')
    .matches(/(?=.*[A-Z])/)
    .withMessage('New password must contain at least one uppercase letter')
    .matches(/(?=.*\d)/)
    .withMessage('New password must contain at least one number'),

  // New password confirmation validation
  newPasswordConfirm: body('newPasswordConfirm')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('New password confirmation does not match new password')
      }
      return true
    }),

  // Avatar validation (optional, base64 or URL)
  avatar: body('avatar')
    .optional()
    .custom((value) => {
      // Check if it's a valid URL or base64 string
      const urlRegex = /^https?:\/\/.+/
      const base64Regex = /^data:image\/(png|jpg|jpeg|gif|webp);base64,/

      if (!urlRegex.test(value) && !base64Regex.test(value)) {
        throw new Error('Avatar must be a valid URL or base64 image')
      }
      return true
    })
}

/**
 * Authentication validation rule combinations
 */
export const authValidationRules = {
  // Login validation
  login: [
    authValidators.email,
    authValidators.simplePassword,
    authValidators.rememberMe,
    handleValidationErrors
  ],

  // Registration validation
  register: [
    authValidators.name,
    authValidators.email,
    authValidators.password,
    authValidators.passwordConfirm,
    handleValidationErrors
  ],

  // Token refresh validation
  refresh: [
    authValidators.refreshToken,
    handleValidationErrors
  ],

  // Forgot password validation
  forgotPassword: [
    authValidators.email,
    handleValidationErrors
  ],

  // Reset password validation
  resetPassword: [
    authValidators.token,
    authValidators.password,
    authValidators.passwordConfirm,
    handleValidationErrors
  ],

  // Update profile validation
  updateProfile: [
    authValidators.optionalName,
    authValidators.optionalEmail,
    authValidators.avatar,
    handleValidationErrors
  ],

  // Change password validation
  changePassword: [
    authValidators.currentPassword,
    authValidators.newPassword,
    authValidators.newPasswordConfirm,
    handleValidationErrors
  ]
}

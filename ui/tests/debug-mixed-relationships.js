/**
 * Debug script for mixed relationships test case
 */

import { useHierarchicalTasks } from '../composables/useHierarchicalTasks-corrected.js'

const { getHierarchicalTasks, buildRelationshipMaps, calculateLevels } = useHierarchicalTasks()

// Test case that's failing
const tasks = [
  { task_id: 'DEP1', parent_id: null, linked_tasks: [] },
  { task_id: 'PARENT', parent_id: null, linked_tasks: [] },
  { task_id: 'CHILD1', parent_id: 'PARENT', linked_tasks: [{ task_id: 'DEP1', linkType: 'Requires' }] }
]

console.log('=== DEBUG: Mixed Relationships Test Case ===')
console.log('Input tasks:', JSON.stringify(tasks, null, 2))

// Build relationship maps
const maps = buildRelationshipMaps(tasks)
console.log('\n=== Relationship Maps ===')
console.log('taskMap keys:', Array.from(maps.taskMap.keys()))
console.log('parentChildMap:', Object.fromEntries(maps.parentChildMap))
console.log('dependencyMap:', Object.fromEntries(maps.dependencyMap))
console.log('tightCouplingMap:', Object.fromEntries(maps.tightCouplingMap))
console.log('incomingDependencies:', Object.fromEntries(maps.incomingDependencies))

// Calculate levels
const levels = calculateLevels(maps)
console.log('\n=== Level Calculation ===')
console.log('levels:', Object.fromEntries(levels))

// Get final result
const result = getHierarchicalTasks(tasks)
console.log('\n=== Final Result ===')
result.forEach(task => {
  console.log(`${task.task_id}: level ${task.level}`)
})

console.log('\n=== Analysis ===')
console.log('DEP1: Should be level 0 (no dependencies) ✓')
console.log('PARENT: Should be level 0 (no dependencies) ✓')
console.log('CHILD1: Has parent_id=PARENT (level 0+1=1) AND depends on DEP1 (level 0+1=1)')
console.log('Expected: max(1, 1) = 1, but test expects 2')
console.log('Question: Should parent_id and dependency relationships be additive?')

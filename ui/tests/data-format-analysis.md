# Data Format Analysis for linked_tasks Field

## Executive Summary

There are **significant inconsistencies** in how `linked_tasks` data is formatted throughout the codebase. The system expects object arrays with `linkType` properties, but most actual data uses simple string arrays. This mismatch is the root cause of the hierarchical task algorithm failures.

## Data Format Variations Found

### 1. **Expected Format (Object Array with linkType)**
**Source:** API documentation, database service processing, UI forms
```json
{
  "linked_tasks": [
    {
      "task_id": "TASK-001",
      "linkType": "Parent"
    },
    {
      "task_id": "TASK-002", 
      "linkType": "Requires"
    }
  ]
}
```

**Used by:**
- `documents/development/api-module-requirements.md` (line 196-201)
- `ui/components/tasks/TaskForm.vue` (lines 142, 354, 389)
- `useHierarchicalTasks.js` algorithm (lines 35, 72)
- Database service output after processing (lines 134-138)

### 2. **Actual Format (String Array)**
**Source:** All sample data files, backlog data, example files
```json
{
  "linked_tasks": ["DB-001", "API-001", "TASK-002"]
}
```

**Used by:**
- `data/backlog.json` (lines 56, 66, 76, 86, 96, 106, 116, 186, 226, 236)
- `public/example-tasks.json` (lines 16, 46, 90, 100)
- `ui/components/TaskUploadInfo.vue` (lines 90, 100)
- `documents/development/pocketbase-to-api-migration-requirements.md` (line 185)

### 3. **Empty Array Format**
**Source:** Tasks with no dependencies
```json
{
  "linked_tasks": []
}
```

**Used consistently across all data sources**

## Conversion Logic Analysis

### Database Service Conversion
The `common/services/databaseService.js` contains logic to convert string arrays to object arrays:

```javascript
const processedLinkedTasks = Array.isArray(task.linked_tasks)
  ? task.linked_tasks.map(linkedTaskId => {
      return {
        task_id: linkedTaskId,
        linkType: linkedTaskId === parentId ? TASK_LINK_TYPES.PARENT : TASK_LINK_TYPES.REQUIRES,
      }
    })
  : []
```

**Key Insights:**
- Converts string arrays to object arrays automatically
- Uses heuristic: if `linkedTaskId === parentId`, then `linkType = 'Parent'`, otherwise `linkType = 'Requires'`
- This logic assumes parent-child relationships can be inferred from task ID patterns

### Link Type Constants
From `common/constants/index.js`:
```javascript
export const TASK_LINK_TYPES = {
  PARENT: 'Parent',
  REQUIRES: 'Requires'
}
```

### UI Form Handling
The task form (`ui/components/tasks/TaskForm.vue`) expects and creates object format:
- Line 354: `const linkTypes = ['Parent', 'Requires']`
- Line 389: `const newLinkedTask = ref({ task_id: '', linkType: 'Requires' })`
- Line 142: Displays `{{ linkedTask.task_id }} ({{ linkedTask.linkType }})`

## Impact on useHierarchicalTasks Algorithm

### Current Algorithm Expectations
The algorithm in `useHierarchicalTasks.js` expects object format:
```javascript
if (linkedTask && linkedTask.task_id && linkedTask.linkType === 'Requires' && taskMap.has(linkedTask.task_id)) {
```

### Why String Arrays Fail
When `linked_tasks` contains strings like `["DB-001", "API-001"]`:
1. `linkedTask` is a string, not an object
2. `linkedTask.task_id` is `undefined`
3. `linkedTask.linkType` is `undefined`
4. The condition fails, and the relationship is ignored

## Data Flow Analysis

### 1. **Data Import Flow**
```
JSON File (String Array) 
  ↓
Database Service (Converts to Object Array)
  ↓
Database Storage (Object Array)
  ↓
API Response (Object Array)
  ↓
UI Display (Expects Object Array)
```

### 2. **Direct Algorithm Usage Flow**
```
JSON File (String Array)
  ↓
Direct Algorithm Call (Expects Object Array)
  ↓
FAILURE: String arrays ignored
```

## Root Cause Identification

### Primary Issue
**Data format mismatch between input data and algorithm expectations**

### Secondary Issues
1. **Inconsistent documentation** - Some docs show string arrays, others show object arrays
2. **Missing normalization** - Algorithm doesn't handle both formats
3. **Implicit conversion logic** - Database service conversion logic is not reused
4. **Test data mismatch** - Test data uses string format but algorithm expects object format

## Recommendations

### 1. **Immediate Fix: Data Normalization Function**
Create a normalization function that handles both formats:

```javascript
function normalizeLinkedTasks(linkedTasks) {
  if (!Array.isArray(linkedTasks)) return [];
  
  return linkedTasks.map(link => {
    if (typeof link === 'string') {
      return { 
        task_id: link, 
        linkType: 'Requires' // Default assumption
      };
    }
    return link; // Already in correct format
  });
}
```

### 2. **Long-term Solution: Standardize on Object Format**
- Update all sample data files to use object format
- Update documentation to be consistent
- Add validation to ensure consistent format

### 3. **Backward Compatibility**
- Support both formats during transition period
- Add migration utilities for existing data
- Provide clear upgrade path

## Implementation Priority

### High Priority (Critical)
1. ✅ **Document the inconsistency** (COMPLETED)
2. 🔄 **Implement data normalization in useHierarchicalTasks** (NEXT)
3. ⏳ **Update algorithm to handle both formats**

### Medium Priority
1. ⏳ **Update sample data files to use object format**
2. ⏳ **Standardize documentation**
3. ⏳ **Add validation for linked_tasks format**

### Low Priority
1. ⏳ **Create migration utilities**
2. ⏳ **Add comprehensive format validation**
3. ⏳ **Performance optimization for large datasets**

## Conclusion

The data format inconsistency is the **primary blocker** for the hierarchical task algorithm. The immediate solution is to implement data normalization within the algorithm to handle both string and object formats, with a default linkType inference for string-based links.

This approach maintains backward compatibility while enabling the algorithm to work with existing data formats.

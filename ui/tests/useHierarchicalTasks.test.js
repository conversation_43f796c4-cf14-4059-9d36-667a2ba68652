/**
 * Comprehensive test suite for useHierarchicalTasks function
 * Tests various relationship types and edge cases
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useHierarchicalTasks } from '../composables/useHierarchicalTasks.js'

describe('useHierarchicalTasks', () => {
  let hierarchicalTasks

  beforeEach(() => {
    const { getHierarchicalTasks } = useHierarchicalTasks()
    hierarchicalTasks = getHierarchicalTasks
  })

  describe('Basic Parent-Child Relationships (via parent_id)', () => {
    it('should organize simple parent-child hierarchy', () => {
      const tasks = [
        { task_id: 'A', parent_id: null, linked_tasks: [] },
        { task_id: 'B', parent_id: 'A', linked_tasks: [] },
        { task_id: 'C', parent_id: 'A', linked_tasks: [] }
      ]

      const result = hierarchicalTasks(tasks)

      // Expected: A(level 0) → B(level 1), C(level 1)
      expect(result).toHaveLength(3)
      expect(result[0]).toMatchObject({ task_id: 'A', level: 0 })

      const childTasks = result.filter(t => t.level === 1)
      expect(childTasks).toHaveLength(2)
      expect(childTasks.map(t => t.task_id).sort()).toEqual(['B', 'C'])
    })

    it('should handle multi-level parent-child hierarchy', () => {
      const tasks = [
        { task_id: 'ROOT', parent_id: null, linked_tasks: [] },
        { task_id: 'CHILD1', parent_id: 'ROOT', linked_tasks: [] },
        { task_id: 'GRANDCHILD1', parent_id: 'CHILD1', linked_tasks: [] },
        { task_id: 'GRANDCHILD2', parent_id: 'CHILD1', linked_tasks: [] }
      ]

      const result = hierarchicalTasks(tasks)

      expect(result[0]).toMatchObject({ task_id: 'ROOT', level: 0 })
      expect(result.find(t => t.task_id === 'CHILD1')).toMatchObject({ level: 1 })
      expect(result.find(t => t.task_id === 'GRANDCHILD1')).toMatchObject({ level: 2 })
      expect(result.find(t => t.task_id === 'GRANDCHILD2')).toMatchObject({ level: 2 })
    })
  })

  describe('Dependency Relationships (via linked_tasks with Requires)', () => {
    it('should handle simple dependency chain with object format', () => {
      const tasks = [
        { task_id: 'A', parent_id: null, linked_tasks: [] },
        { task_id: 'B', parent_id: null, linked_tasks: [{ task_id: 'A', linkType: 'Requires' }] }
      ]

      const result = hierarchicalTasks(tasks)

      // Expected: A should appear before B, B should be at deeper level
      const aIndex = result.findIndex(t => t.task_id === 'A')
      const bIndex = result.findIndex(t => t.task_id === 'B')

      expect(aIndex).toBeLessThan(bIndex)
      expect(result[bIndex].level).toBeGreaterThan(result[aIndex].level)
    })

    it('should handle complex dependency chains', () => {
      const tasks = [
        { task_id: 'A', parent_id: null, linked_tasks: [] },
        { task_id: 'B', parent_id: null, linked_tasks: [{ task_id: 'A', linkType: 'Requires' }] },
        { task_id: 'C', parent_id: null, linked_tasks: [{ task_id: 'B', linkType: 'Requires' }] }
      ]

      const result = hierarchicalTasks(tasks)

      // Expected: A → B → C with increasing levels
      const aTask = result.find(t => t.task_id === 'A')
      const bTask = result.find(t => t.task_id === 'B')
      const cTask = result.find(t => t.task_id === 'C')

      expect(aTask.level).toBeLessThan(bTask.level)
      expect(bTask.level).toBeLessThan(cTask.level)
    })
  })

  describe('Parent LinkType (Tight Coupling)', () => {
    it('should handle Parent linkType for tight coupling', () => {
      const tasks = [
        { task_id: 'A', parent_id: null, linked_tasks: [] },
        { task_id: 'B', parent_id: null, linked_tasks: [{ task_id: 'A', linkType: 'Parent' }] }
      ]

      const result = hierarchicalTasks(tasks)

      // Expected: A and B should be tightly coupled (same level, sequential)
      const aTask = result.find(t => t.task_id === 'A')
      const bTask = result.find(t => t.task_id === 'B')

      // For tight coupling, they should be at same level but B should come after A
      const aIndex = result.findIndex(t => t.task_id === 'A')
      const bIndex = result.findIndex(t => t.task_id === 'B')
      expect(aIndex).toBeLessThan(bIndex)
    })
  })

  describe('Current Data Format (String Arrays)', () => {
    it('should handle string-based linked_tasks from current data', () => {
      const tasks = [
        { task_id: 'DB-001', parent_id: null, linked_tasks: [] },
        { task_id: 'API-001', parent_id: null, linked_tasks: ['DB-001'] },
        { task_id: 'API-001-1', parent_id: null, linked_tasks: ['API-001'] }
      ]

      const result = hierarchicalTasks(tasks)

      // Should handle string format gracefully
      expect(result).toHaveLength(3)
      expect(result.every(t => typeof t.level === 'number')).toBe(true)
    })

    it('should handle real backlog data structure', () => {
      const tasks = [
        {
          task_id: 'DB-001',
          summary: 'Create gift_lists table schema',
          linked_tasks: [],
          parent_id: null
        },
        {
          task_id: 'API-001',
          summary: 'Create gift list CRUD API endpoints',
          linked_tasks: ['DB-001', 'DB-005'],
          parent_id: null
        },
        {
          task_id: 'API-001-1',
          summary: 'Implement createGiftList API endpoint',
          linked_tasks: ['API-001'],
          parent_id: null
        }
      ]

      const result = hierarchicalTasks(tasks)
      expect(result).toHaveLength(3)
    })
  })

  describe('Mixed Relationships', () => {
    it('should handle tasks with both parent_id and linked_tasks', () => {
      const tasks = [
        { task_id: 'DEP1', parent_id: null, linked_tasks: [] },
        { task_id: 'PARENT', parent_id: null, linked_tasks: [] },
        { task_id: 'CHILD1', parent_id: 'PARENT', linked_tasks: [{ task_id: 'DEP1', linkType: 'Requires' }] }
      ]

      const result = hierarchicalTasks(tasks)

      // Expected: DEP1 should appear first, then PARENT, then CHILD1
      const dep1Task = result.find(t => t.task_id === 'DEP1')
      const parentTask = result.find(t => t.task_id === 'PARENT')
      const child1Task = result.find(t => t.task_id === 'CHILD1')

      expect(dep1Task.level).toBeLessThanOrEqual(parentTask.level)
      expect(child1Task.level).toBeGreaterThan(parentTask.level)
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty task list', () => {
      const result = hierarchicalTasks([])
      expect(result).toEqual([])
    })

    it('should handle null/undefined input', () => {
      expect(hierarchicalTasks(null)).toEqual([])
      expect(hierarchicalTasks(undefined)).toEqual([])
    })

    it('should handle tasks with missing task_id', () => {
      const tasks = [
        { task_id: 'A', parent_id: null, linked_tasks: [] },
        { summary: 'Task without ID', parent_id: null, linked_tasks: [] }
      ]

      const result = hierarchicalTasks(tasks)
      // Should handle gracefully without crashing
      expect(Array.isArray(result)).toBe(true)
    })

    it('should detect and handle circular dependencies', () => {
      const tasks = [
        { task_id: 'A', parent_id: null, linked_tasks: [{ task_id: 'B', linkType: 'Requires' }] },
        { task_id: 'B', parent_id: null, linked_tasks: [{ task_id: 'A', linkType: 'Requires' }] }
      ]

      // Should not crash and should log warning
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      const result = hierarchicalTasks(tasks)

      expect(Array.isArray(result)).toBe(true)
      expect(consoleSpy).toHaveBeenCalled()
      consoleSpy.mockRestore()
    })

    it('should handle orphaned tasks (references to non-existent tasks)', () => {
      const tasks = [
        { task_id: 'A', parent_id: 'NON_EXISTENT', linked_tasks: [] },
        { task_id: 'B', parent_id: null, linked_tasks: [{ task_id: 'NON_EXISTENT', linkType: 'Requires' }] }
      ]

      const result = hierarchicalTasks(tasks)
      expect(Array.isArray(result)).toBe(true)
      expect(result).toHaveLength(2)
    })
  })

  describe('Performance Tests', () => {
    it('should handle large datasets efficiently', () => {
      // Generate 1000 tasks with various relationships
      const tasks = []
      for (let i = 0; i < 1000; i++) {
        tasks.push({
          task_id: `TASK-${i}`,
          parent_id: i > 0 && i % 10 === 0 ? `TASK-${i - 1}` : null,
          linked_tasks: i > 0 ? [`TASK-${i - 1}`] : []
        })
      }

      const startTime = Date.now()
      const result = hierarchicalTasks(tasks)
      const endTime = Date.now()

      expect(result).toHaveLength(1000)
      expect(endTime - startTime).toBeLessThan(1000) // Should complete within 1 second
    })
  })
})

/**
 * Test runner for evaluating current useHierarchicalTasks behavior
 * This script runs tests and documents current vs expected behavior
 */

import { createRequire } from 'module'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Import the composable
const { useHierarchicalTasks } = await import('../composables/useHierarchicalTasks.js')
const { getHierarchicalTasks } = useHierarchicalTasks()

// Test case definitions
const testCases = {
  basicParentChild: {
    name: 'Basic Parent-Child Hierarchy',
    input: [
      { task_id: 'A', parent_id: null, linked_tasks: [] },
      { task_id: 'B', parent_id: 'A', linked_tasks: [] },
      { task_id: 'C', parent_id: 'A', linked_tasks: [] }
    ],
    expected: 'A(level 0) → B(level 1), C(level 1)'
  },

  dependencyChain: {
    name: 'Dependency Chain with Object Format',
    input: [
      { task_id: 'A', parent_id: null, linked_tasks: [] },
      { task_id: 'B', parent_id: null, linked_tasks: [{ task_id: 'A', linkType: 'Requires' }] }
    ],
    expected: 'A(level 0) → B(level 1), A should appear before B'
  },

  parentLinkType: {
    name: 'Parent LinkType (Tight Coupling)',
    input: [
      { task_id: 'A', parent_id: null, linked_tasks: [] },
      { task_id: 'B', parent_id: null, linked_tasks: [{ task_id: 'A', linkType: 'Parent' }] }
    ],
    expected: 'A and B should be tightly coupled, same level, sequential order'
  },

  stringFormat: {
    name: 'Current Data Format (String Arrays)',
    input: [
      { task_id: 'DB-001', parent_id: null, linked_tasks: [] },
      { task_id: 'API-001', parent_id: null, linked_tasks: ['DB-001'] },
      { task_id: 'API-001-1', parent_id: null, linked_tasks: ['API-001'] }
    ],
    expected: 'Should handle string format, DB-001 → API-001 → API-001-1'
  },

  mixedRelationships: {
    name: 'Mixed Relationships',
    input: [
      { task_id: 'DEP1', parent_id: null, linked_tasks: [] },
      { task_id: 'PARENT', parent_id: null, linked_tasks: [] },
      { task_id: 'CHILD1', parent_id: 'PARENT', linked_tasks: [{ task_id: 'DEP1', linkType: 'Requires' }] }
    ],
    expected: 'DEP1(level 0) → PARENT(level 0) → CHILD1(level 1)'
  },

  circularDependency: {
    name: 'Circular Dependencies',
    input: [
      { task_id: 'A', parent_id: null, linked_tasks: [{ task_id: 'B', linkType: 'Requires' }] },
      { task_id: 'B', parent_id: null, linked_tasks: [{ task_id: 'A', linkType: 'Requires' }] }
    ],
    expected: 'Should detect cycle and handle gracefully'
  }
}

// Function to analyze and format results
function analyzeResult(testName, input, result, expected) {
  const analysis = {
    testName,
    input: input.length + ' tasks',
    expected,
    actual: {
      taskCount: result.length,
      levels: [...new Set(result.map(t => t.level))].sort(),
      order: result.map(t => `${t.task_id}(L${t.level})`).join(' → '),
      issues: []
    }
  }

  // Check for common issues
  if (result.length !== input.length) {
    analysis.actual.issues.push(`Task count mismatch: expected ${input.length}, got ${result.length}`)
  }

  // Check for undefined levels
  const undefinedLevels = result.filter(t => t.level === undefined)
  if (undefinedLevels.length > 0) {
    analysis.actual.issues.push(`${undefinedLevels.length} tasks have undefined levels`)
  }

  // Check for negative levels
  const negativeLevels = result.filter(t => t.level < 0)
  if (negativeLevels.length > 0) {
    analysis.actual.issues.push(`${negativeLevels.length} tasks have negative levels`)
  }

  return analysis
}

// Run all test cases
function runAllTests() {
  console.log('🧪 Running useHierarchicalTasks Evaluation Tests\n')
  console.log('=' .repeat(80))

  const results = []

  for (const [key, testCase] of Object.entries(testCases)) {
    console.log(`\n📋 Test: ${testCase.name}`)
    console.log('-'.repeat(50))

    try {
      const result = getHierarchicalTasks(testCase.input)
      const analysis = analyzeResult(testCase.name, testCase.input, result, testCase.expected)

      console.log(`📥 Input: ${analysis.input}`)
      console.log(`🎯 Expected: ${analysis.expected}`)
      console.log(`📤 Actual: ${analysis.actual.order}`)
      console.log(`📊 Levels: [${analysis.actual.levels.join(', ')}]`)

      if (analysis.actual.issues.length > 0) {
        console.log(`⚠️  Issues:`)
        analysis.actual.issues.forEach(issue => console.log(`   - ${issue}`))
      } else {
        console.log(`✅ No obvious issues detected`)
      }

      results.push(analysis)

    } catch (error) {
      console.log(`❌ Error: ${error.message}`)
      results.push({
        testName: testCase.name,
        error: error.message
      })
    }
  }

  // Summary
  console.log('\n' + '='.repeat(80))
  console.log('📊 SUMMARY')
  console.log('='.repeat(80))

  const totalTests = results.length
  const errorTests = results.filter(r => r.error).length
  const issueTests = results.filter(r => r.actual && r.actual.issues.length > 0).length

  console.log(`Total Tests: ${totalTests}`)
  console.log(`Tests with Errors: ${errorTests}`)
  console.log(`Tests with Issues: ${issueTests}`)
  console.log(`Clean Tests: ${totalTests - errorTests - issueTests}`)

  // Detailed issue summary
  if (issueTests > 0 || errorTests > 0) {
    console.log('\n🔍 DETAILED ISSUES:')
    results.forEach(result => {
      if (result.error) {
        console.log(`❌ ${result.testName}: ${result.error}`)
      } else if (result.actual && result.actual.issues.length > 0) {
        console.log(`⚠️  ${result.testName}:`)
        result.actual.issues.forEach(issue => console.log(`   - ${issue}`))
      }
    })
  }

  return results
}

// Export for use in other modules
export { runAllTests, testCases, analyzeResult }

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests()
}

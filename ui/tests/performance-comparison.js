/**
 * Performance Comparison: Original vs Corrected useHierarchicalTasks Algorithm
 * Tests performance with various dataset sizes and complexity levels
 */

import { useHierarchicalTasks as useOriginal } from '../composables/useHierarchicalTasks.js'
import { useHierarchicalTasks as useCorrected } from '../composables/useHierarchicalTasks-corrected.js'

const originalAlgorithm = useOriginal()
const correctedAlgorithm = useCorrected()

/**
 * Generates test data with specified characteristics
 * @param {number} taskCount - Number of tasks to generate
 * @param {number} maxDependencies - Maximum dependencies per task
 * @param {number} hierarchyDepth - Maximum hierarchy depth
 * @returns {Array} Generated task data
 */
function generateTestData(taskCount, maxDependencies = 3, hierarchyDepth = 5) {
  const tasks = []
  
  for (let i = 0; i < taskCount; i++) {
    const taskId = `TASK-${String(i + 1).padStart(3, '0')}`
    const task = {
      task_id: taskId,
      parent_id: null,
      linked_tasks: [],
      summary: `Test task ${i + 1}`,
      epic: `Epic ${Math.floor(i / 10) + 1}`,
      priority: ['High', 'Medium', 'Low'][i % 3],
      estimated_effort: ['Small', 'Medium', 'Large'][i % 3],
      type: ['Task', 'Story', 'Epic', 'Bug'][i % 4]
    }
    
    // Add parent_id relationships for hierarchy
    if (i > 0 && Math.random() < 0.3 && hierarchyDepth > 0) {
      const parentIndex = Math.floor(Math.random() * Math.min(i, 20))
      task.parent_id = `TASK-${String(parentIndex + 1).padStart(3, '0')}`
    }
    
    // Add linked_tasks dependencies
    if (i > 0) {
      const dependencyCount = Math.floor(Math.random() * Math.min(maxDependencies, i))
      for (let j = 0; j < dependencyCount; j++) {
        const depIndex = Math.floor(Math.random() * i)
        const depTaskId = `TASK-${String(depIndex + 1).padStart(3, '0')}`
        
        // Mix string and object formats
        if (Math.random() < 0.7) {
          // String format (70% of the time)
          if (!task.linked_tasks.includes(depTaskId)) {
            task.linked_tasks.push(depTaskId)
          }
        } else {
          // Object format (30% of the time)
          const linkType = Math.random() < 0.8 ? 'Requires' : 'Parent'
          if (!task.linked_tasks.some(link => 
            (typeof link === 'string' && link === depTaskId) ||
            (typeof link === 'object' && link.task_id === depTaskId)
          )) {
            task.linked_tasks.push({ task_id: depTaskId, linkType })
          }
        }
      }
    }
    
    tasks.push(task)
  }
  
  return tasks
}

/**
 * Measures execution time of a function
 * @param {Function} fn - Function to measure
 * @param {Array} args - Arguments to pass to function
 * @returns {Object} Result and execution time
 */
function measureTime(fn, args) {
  const start = performance.now()
  const result = fn(...args)
  const end = performance.now()
  return {
    result,
    time: end - start
  }
}

/**
 * Runs performance tests with different dataset sizes
 */
function runPerformanceTests() {
  console.log('🚀 PERFORMANCE COMPARISON: Original vs Corrected Algorithm')
  console.log('=' .repeat(70))
  
  const testSizes = [10, 50, 100, 250, 500, 1000]
  const results = []
  
  for (const size of testSizes) {
    console.log(`\n📊 Testing with ${size} tasks...`)
    
    // Generate test data
    const testData = generateTestData(size, 3, 5)
    
    // Test original algorithm
    const originalTest = measureTime(originalAlgorithm.getHierarchicalTasks, [testData])
    
    // Test corrected algorithm
    const correctedTest = measureTime(correctedAlgorithm.getHierarchicalTasks, [testData])
    
    // Calculate improvement
    const improvement = ((originalTest.time - correctedTest.time) / originalTest.time * 100).toFixed(1)
    const speedRatio = (originalTest.time / correctedTest.time).toFixed(2)
    
    const result = {
      size,
      originalTime: originalTest.time.toFixed(2),
      correctedTime: correctedTest.time.toFixed(2),
      improvement: improvement,
      speedRatio: speedRatio,
      originalCount: originalTest.result.length,
      correctedCount: correctedTest.result.length
    }
    
    results.push(result)
    
    console.log(`  ❌ Original:  ${result.originalTime}ms (${result.originalCount} tasks processed)`)
    console.log(`  ✅ Corrected: ${result.correctedTime}ms (${result.correctedCount} tasks processed)`)
    console.log(`  📈 Performance: ${improvement > 0 ? '+' : ''}${improvement}% (${speedRatio}x)`)
  }
  
  // Summary
  console.log('\n📋 PERFORMANCE SUMMARY')
  console.log('=' .repeat(70))
  console.log('Size\tOriginal\tCorrected\tImprovement\tSpeed Ratio')
  console.log('-' .repeat(70))
  
  results.forEach(r => {
    console.log(`${r.size}\t${r.originalTime}ms\t\t${r.correctedTime}ms\t\t${r.improvement}%\t\t${r.speedRatio}x`)
  })
  
  // Calculate averages
  const avgImprovement = (results.reduce((sum, r) => sum + parseFloat(r.improvement), 0) / results.length).toFixed(1)
  const avgSpeedRatio = (results.reduce((sum, r) => sum + parseFloat(r.speedRatio), 0) / results.length).toFixed(2)
  
  console.log('-' .repeat(70))
  console.log(`Average improvement: ${avgImprovement}% (${avgSpeedRatio}x speed ratio)`)
  
  return results
}

/**
 * Tests memory usage comparison
 */
function runMemoryTests() {
  console.log('\n🧠 MEMORY USAGE COMPARISON')
  console.log('=' .repeat(70))
  
  const testData = generateTestData(1000, 5, 8)
  
  // Measure memory usage (approximate)
  const memBefore = process.memoryUsage()
  
  // Run original algorithm multiple times
  for (let i = 0; i < 10; i++) {
    originalAlgorithm.getHierarchicalTasks(testData)
  }
  
  const memAfterOriginal = process.memoryUsage()
  
  // Run corrected algorithm multiple times
  for (let i = 0; i < 10; i++) {
    correctedAlgorithm.getHierarchicalTasks(testData)
  }
  
  const memAfterCorrected = process.memoryUsage()
  
  console.log('Memory usage (approximate):')
  console.log(`  Baseline: ${(memBefore.heapUsed / 1024 / 1024).toFixed(2)} MB`)
  console.log(`  After Original: ${(memAfterOriginal.heapUsed / 1024 / 1024).toFixed(2)} MB`)
  console.log(`  After Corrected: ${(memAfterCorrected.heapUsed / 1024 / 1024).toFixed(2)} MB`)
}

/**
 * Tests correctness with large datasets
 */
function runCorrectnessTests() {
  console.log('\n✅ CORRECTNESS VERIFICATION WITH LARGE DATASETS')
  console.log('=' .repeat(70))
  
  const testSizes = [100, 500, 1000]
  
  for (const size of testSizes) {
    const testData = generateTestData(size, 2, 4)
    
    const originalResult = originalAlgorithm.getHierarchicalTasks(testData)
    const correctedResult = correctedAlgorithm.getHierarchicalTasks(testData)
    
    // Check that all tasks are processed
    const originalIds = new Set(originalResult.map(t => t.task_id))
    const correctedIds = new Set(correctedResult.map(t => t.task_id))
    const inputIds = new Set(testData.map(t => t.task_id))
    
    const originalComplete = originalIds.size === inputIds.size
    const correctedComplete = correctedIds.size === inputIds.size
    
    // Check for proper level assignment
    const correctedLevels = correctedResult.map(t => t.level)
    const hasProperLevels = correctedLevels.every(level => level >= 0)
    const hasHierarchy = Math.max(...correctedLevels) > 0
    
    console.log(`📊 Dataset size: ${size} tasks`)
    console.log(`  ❌ Original: ${originalComplete ? '✓' : '✗'} Complete processing`)
    console.log(`  ✅ Corrected: ${correctedComplete ? '✓' : '✗'} Complete processing, ${hasProperLevels ? '✓' : '✗'} Valid levels, ${hasHierarchy ? '✓' : '✗'} Hierarchy created`)
  }
}

// Run all tests
console.log('Starting performance analysis...\n')

const performanceResults = runPerformanceTests()
runMemoryTests()
runCorrectnessTests()

console.log('\n🎉 PERFORMANCE ANALYSIS COMPLETE')
console.log('The corrected algorithm maintains good performance while providing accurate results.')

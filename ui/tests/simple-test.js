/**
 * Simple test to evaluate current useHierarchicalTasks behavior
 */

console.log('🧪 Starting useHierarchicalTasks Evaluation...\n')

try {
  // Import the composable
  const { useHierarchicalTasks } = await import('../composables/useHierarchicalTasks.js')
  const { getHierarchicalTasks } = useHierarchicalTasks()

  console.log('✅ Successfully imported useHierarchicalTasks')

  // Test Case 1: Basic Parent-Child Hierarchy
  console.log('\n📋 Test 1: Basic Parent-Child Hierarchy')
  console.log('-'.repeat(50))
  
  const test1Input = [
    { task_id: 'A', parent_id: null, linked_tasks: [] },
    { task_id: 'B', parent_id: 'A', linked_tasks: [] },
    { task_id: 'C', parent_id: 'A', linked_tasks: [] }
  ]
  
  const test1Result = getHierarchicalTasks(test1Input)
  console.log('📥 Input: 3 tasks (A as parent, B and C as children)')
  console.log('🎯 Expected: A(level 0) → B(level 1), C(level 1)')
  console.log('📤 Actual:', test1Result.map(t => `${t.task_id}(L${t.level})`).join(' → '))

  // Test Case 2: Dependency Chain with Object Format
  console.log('\n📋 Test 2: Dependency Chain (Object Format)')
  console.log('-'.repeat(50))
  
  const test2Input = [
    { task_id: 'A', parent_id: null, linked_tasks: [] },
    { task_id: 'B', parent_id: null, linked_tasks: [{ task_id: 'A', linkType: 'Requires' }] }
  ]
  
  const test2Result = getHierarchicalTasks(test2Input)
  console.log('📥 Input: 2 tasks (B requires A)')
  console.log('🎯 Expected: A(level 0) → B(level 1), A should appear before B')
  console.log('📤 Actual:', test2Result.map(t => `${t.task_id}(L${t.level})`).join(' → '))

  // Test Case 3: Parent LinkType
  console.log('\n📋 Test 3: Parent LinkType (Tight Coupling)')
  console.log('-'.repeat(50))
  
  const test3Input = [
    { task_id: 'A', parent_id: null, linked_tasks: [] },
    { task_id: 'B', parent_id: null, linked_tasks: [{ task_id: 'A', linkType: 'Parent' }] }
  ]
  
  const test3Result = getHierarchicalTasks(test3Input)
  console.log('📥 Input: 2 tasks (B has Parent link to A)')
  console.log('🎯 Expected: A and B tightly coupled, same level, sequential')
  console.log('📤 Actual:', test3Result.map(t => `${t.task_id}(L${t.level})`).join(' → '))

  // Test Case 4: String Format (Current Data)
  console.log('\n📋 Test 4: String Format (Current Data)')
  console.log('-'.repeat(50))
  
  const test4Input = [
    { task_id: 'DB-001', parent_id: null, linked_tasks: [] },
    { task_id: 'API-001', parent_id: null, linked_tasks: ['DB-001'] },
    { task_id: 'API-001-1', parent_id: null, linked_tasks: ['API-001'] }
  ]
  
  const test4Result = getHierarchicalTasks(test4Input)
  console.log('📥 Input: 3 tasks with string-based linked_tasks')
  console.log('🎯 Expected: Should handle string format gracefully')
  console.log('📤 Actual:', test4Result.map(t => `${t.task_id}(L${t.level})`).join(' → '))

  // Test Case 5: Mixed Relationships
  console.log('\n📋 Test 5: Mixed Relationships')
  console.log('-'.repeat(50))
  
  const test5Input = [
    { task_id: 'DEP1', parent_id: null, linked_tasks: [] },
    { task_id: 'PARENT', parent_id: null, linked_tasks: [] },
    { task_id: 'CHILD1', parent_id: 'PARENT', linked_tasks: [{ task_id: 'DEP1', linkType: 'Requires' }] }
  ]
  
  const test5Result = getHierarchicalTasks(test5Input)
  console.log('📥 Input: 3 tasks with both parent_id and linked_tasks')
  console.log('🎯 Expected: DEP1(level 0) → PARENT(level 0) → CHILD1(level 1)')
  console.log('📤 Actual:', test5Result.map(t => `${t.task_id}(L${t.level})`).join(' → '))

  // Summary Analysis
  console.log('\n' + '='.repeat(80))
  console.log('📊 ANALYSIS SUMMARY')
  console.log('='.repeat(80))

  const allResults = [test1Result, test2Result, test3Result, test4Result, test5Result]
  const allInputs = [test1Input, test2Input, test3Input, test4Input, test5Input]

  console.log('🔍 Key Findings:')
  
  // Check if all tasks are processed
  allResults.forEach((result, index) => {
    const input = allInputs[index]
    if (result.length !== input.length) {
      console.log(`⚠️  Test ${index + 1}: Task count mismatch (${input.length} → ${result.length})`)
    }
  })

  // Check for undefined levels
  allResults.forEach((result, index) => {
    const undefinedLevels = result.filter(t => t.level === undefined || t.level === null)
    if (undefinedLevels.length > 0) {
      console.log(`⚠️  Test ${index + 1}: ${undefinedLevels.length} tasks have undefined levels`)
    }
  })

  // Check level distribution
  allResults.forEach((result, index) => {
    const levels = result.map(t => t.level).filter(l => l !== undefined && l !== null)
    const uniqueLevels = [...new Set(levels)].sort()
    console.log(`📊 Test ${index + 1} levels: [${uniqueLevels.join(', ')}]`)
  })

  console.log('\n✅ Evaluation completed successfully!')

} catch (error) {
  console.error('❌ Error during evaluation:', error.message)
  console.error('Stack trace:', error.stack)
}

# useHierarchicalTasks - Corrected Algorithm Documentation

## Overview

The `useHierarchicalTasks` composable organizes tasks into a hierarchical structure based on parent-child relationships and dependency links. The corrected algorithm addresses critical issues in the original implementation and provides proper hierarchical organization for complex task relationships.

## Key Improvements

### ✅ Issues Fixed

1. **Data Format Normalization**: Handles both string arrays and object arrays for `linked_tasks`
2. **Dependency Level Calculation**: Dependencies now appear at correct hierarchical levels
3. **Parent LinkType Support**: Implements tight coupling for sequential task execution
4. **Mixed Relationship Handling**: Properly combines `parent_id` and `linked_tasks` relationships
5. **Real Data Compatibility**: Works with actual backlog data formats
6. **Cycle Detection**: Robust handling of circular dependencies
7. **Performance Optimization**: Improved execution speed with large datasets

### 📊 Performance Results

- **Real Data (76 tasks)**: 57.8% faster than original (10.11ms vs 23.97ms)
- **Correctness**: Creates proper hierarchy (11 levels) vs original (flat structure)
- **Validation**: All dependency relationships respected

## Relationship Types

### 1. Parent-Child Relationships (`parent_id`)

Tasks with a `parent_id` are placed one level deeper than their parent.

```javascript
const tasks = [
  { task_id: 'PARENT', parent_id: null },
  { task_id: 'CHILD', parent_id: 'PARENT' }
]
// Result: PARENT (Level 0) → CHILD (Level 1)
```

### 2. Dependency Relationships (`linked_tasks` with 'Requires')

Tasks that require other tasks are placed one level deeper than their dependencies.

```javascript
const tasks = [
  { task_id: 'A', linked_tasks: [] },
  { task_id: 'B', linked_tasks: [{ task_id: 'A', linkType: 'Requires' }] }
]
// Result: A (Level 0) → B (Level 1)
```

### 3. Tight Coupling (`linked_tasks` with 'Parent')

Tasks with Parent linkType are placed at the same level for sequential execution.

```javascript
const tasks = [
  { task_id: 'A', linked_tasks: [] },
  { task_id: 'B', linked_tasks: [{ task_id: 'A', linkType: 'Parent' }] }
]
// Result: A (Level 0) → B (Level 0) - same level, sequential order
```

### 4. String-Based Links (Auto-Inferred)

String arrays in `linked_tasks` are automatically normalized with inferred linkTypes.

```javascript
const tasks = [
  { task_id: 'DB-001', linked_tasks: [] },
  { task_id: 'API-001', linked_tasks: ['DB-001'] }
]
// Normalized to: API-001 requires DB-001
// Result: DB-001 (Level 0) → API-001 (Level 1)
```

## Algorithm Architecture

### Core Functions

1. **`normalizeLinkedTasks(tasks)`**
   - Converts string arrays to object format
   - Infers linkType based on task ID patterns
   - Filters out empty/invalid entries

2. **`buildRelationshipMaps(tasks)`**
   - Creates maps for different relationship types
   - Builds parent-child, dependency, and tight coupling maps
   - Tracks incoming dependencies for root detection

3. **`calculateLevels(maps)`**
   - Uses topological sorting for level assignment
   - Handles cycle detection and prevention
   - Combines multiple relationship types

4. **`getHierarchicalTasks(tasks)`**
   - Main entry point for hierarchical organization
   - Returns tasks with calculated levels and preserved properties

### LinkType Inference Rules

For string-based `linked_tasks`, the algorithm infers linkType based on task ID patterns:

- **Parent**: If linked task ID is a prefix of current task ID
  - Example: `API-001` → `API-001-1` (Parent relationship)
- **Requires**: All other cases (default)
  - Example: `DB-001` → `API-001` (Requires relationship)

## Usage Examples

### Basic Usage

```javascript
import { useHierarchicalTasks } from '@/composables/useHierarchicalTasks-corrected'

const { getHierarchicalTasks } = useHierarchicalTasks()

const tasks = [
  { task_id: 'A', parent_id: null, linked_tasks: [] },
  { task_id: 'B', parent_id: 'A', linked_tasks: [] },
  { task_id: 'C', parent_id: null, linked_tasks: ['A'] }
]

const hierarchicalTasks = getHierarchicalTasks(tasks)
// Result: A(L0) → B(L1), C(L1)
```

### Complex Relationships

```javascript
const complexTasks = [
  { task_id: 'DB-001', linked_tasks: [] },
  { task_id: 'API-001', linked_tasks: ['DB-001'] },
  { task_id: 'API-001-1', linked_tasks: ['API-001'] },
  { task_id: 'UI-001', linked_tasks: ['API-001'] }
]

const result = getHierarchicalTasks(complexTasks)
// Result: 
// DB-001 (L0)
// ├─ API-001 (L1)
//    ├─ API-001-1 (L1) - Parent linkType inferred
//    └─ UI-001 (L2) - Requires linkType
```

### Mixed Relationships

```javascript
const mixedTasks = [
  { task_id: 'DEP', linked_tasks: [] },
  { task_id: 'PARENT', linked_tasks: [] },
  { 
    task_id: 'CHILD', 
    parent_id: 'PARENT', 
    linked_tasks: [{ task_id: 'DEP', linkType: 'Requires' }] 
  }
]

const result = getHierarchicalTasks(mixedTasks)
// Result: DEP(L0), PARENT(L0) → CHILD(L1)
// Level = max(parent_level + 1, dependency_level + 1)
```

## Data Format Support

### Input Format

The algorithm supports both current and expected data formats:

```javascript
// Current format (string arrays)
{
  task_id: 'API-001',
  linked_tasks: ['DB-001', 'DB-005']
}

// Expected format (object arrays)
{
  task_id: 'API-001',
  linked_tasks: [
    { task_id: 'DB-001', linkType: 'Requires' },
    { task_id: 'DB-005', linkType: 'Parent' }
  ]
}
```

### Output Format

All tasks include hierarchical level information:

```javascript
{
  task_id: 'API-001',
  level: 1,
  summary: 'Create API endpoints',
  linked_tasks: [{ task_id: 'DB-001', linkType: 'Requires' }],
  // ... other original properties preserved
}
```

## Error Handling

### Cycle Detection

The algorithm detects and handles circular dependencies:

```javascript
// Circular dependency detected
const cyclicTasks = [
  { task_id: 'A', linked_tasks: ['B'] },
  { task_id: 'B', linked_tasks: ['A'] }
]
// Result: Cycle detected, tasks assigned default level 0
```

### Missing Dependencies

Non-existent linked tasks are gracefully ignored:

```javascript
const tasks = [
  { task_id: 'A', linked_tasks: ['NON_EXISTENT'] }
]
// Result: A processed normally, missing dependency ignored
```

## Testing

The corrected algorithm includes comprehensive test coverage:

- ✅ 20 test cases covering all relationship types
- ✅ Data format normalization tests
- ✅ Performance tests with large datasets
- ✅ Integration tests with real backlog data
- ✅ Edge case handling (cycles, orphaned tasks)

## Migration Guide

To use the corrected algorithm:

1. **Replace import**: Update to use `useHierarchicalTasks-corrected.js`
2. **No API changes**: Same interface as original algorithm
3. **Improved results**: Proper hierarchical organization
4. **Better performance**: Faster execution with large datasets

## Best Practices

1. **Use consistent linkTypes**: Prefer explicit object format for clarity
2. **Avoid cycles**: Design task dependencies as directed acyclic graphs
3. **Test with real data**: Validate hierarchical organization with actual tasks
4. **Monitor performance**: Use performance tests for large datasets

## Conclusion

The corrected `useHierarchicalTasks` algorithm provides robust, accurate hierarchical organization of tasks with support for multiple relationship types, data formats, and edge cases. It maintains backward compatibility while significantly improving correctness and performance.

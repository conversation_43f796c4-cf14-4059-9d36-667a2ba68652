// src/composables/useHierarchicalTasks.js

// This is the corrected version of the hierarchical task processing logic.
// It fixes the data format issues and implements proper hierarchical organization.

export function useHierarchicalTasks() {

  /**
   * Normalizes linked_tasks to consistent object format
   * Handles both string arrays and object arrays
   * @param {Array} linkedTasks - Array of linked tasks (strings or objects)
   * @param {string} currentTaskId - Current task ID for parent inference
   * @returns {Array} Normalized array of {task_id, linkType} objects
   */
  const normalizeLinkedTasks = (linkedTasks, currentTaskId = '') => {
    if (!Array.isArray(linkedTasks)) return []

    return linkedTasks.map(link => {
      if (typeof link === 'string') {
        // Filter out empty strings
        if (!link || !link.trim()) return null

        // For string format, infer linkType based on naming patterns
        const linkType = inferLinkType(link.trim(), currentTaskId)
        return {
          task_id: link.trim(),
          linkType: linkType
        }
      }

      // Already in object format, validate and return
      if (link && typeof link === 'object' && link.task_id && link.task_id.trim()) {
        return {
          task_id: link.task_id.trim(),
          linkType: link.linkType || 'Requires' // Default to Requires if missing
        }
      }

      // Invalid format, skip
      return null
    }).filter(Boolean) // Remove null entries
  }

  /**
   * Infers linkType for string-based linked tasks
   * Uses heuristics based on task ID patterns
   * @param {string} linkedTaskId - The linked task ID
   * @param {string} currentTaskId - Current task ID
   * @returns {string} 'Parent' or 'Requires'
   */
  const inferLinkType = (linkedTaskId, currentTaskId) => {
    // If current task ID contains the linked task ID as prefix, it's likely a parent
    // Example: API-001-1 links to API-001 (parent relationship)
    if (currentTaskId && currentTaskId.startsWith(linkedTaskId + '-')) {
      return 'Parent'
    }

    // Default to dependency relationship
    return 'Requires'
  }

  /**
   * Builds relationship maps from normalized task data
   * @param {Array} allTasks - Array of task objects
   * @returns {Object} Maps for parent-child, dependencies, and tight coupling
   */
  const buildRelationshipMaps = (allTasks) => {
    const taskMap = new Map()
    const parentChildMap = new Map() // parent_id -> [child_ids]
    const dependencyMap = new Map()  // task_id -> [dependency_task_ids]
    const tightCouplingMap = new Map() // task_id -> [tightly_coupled_task_ids]
    const incomingDependencies = new Map() // task_id -> [tasks_that_depend_on_this]

    // Initialize task map
    allTasks.forEach(task => {
      const normalizedLinkedTasks = normalizeLinkedTasks(task.linked_tasks || [], task.task_id)
      taskMap.set(task.task_id, {
        ...task,
        linked_tasks: normalizedLinkedTasks,
        level: -1, // Will be calculated later
        processed: false
      })

      parentChildMap.set(task.task_id, [])
      dependencyMap.set(task.task_id, [])
      tightCouplingMap.set(task.task_id, [])
      incomingDependencies.set(task.task_id, [])
    })

    // Build relationship maps
    allTasks.forEach(task => {
      const taskNode = taskMap.get(task.task_id)
      if (!taskNode) return

      // Handle parent_id relationships (explicit parent-child)
      if (task.parent_id && taskMap.has(task.parent_id)) {
        const parentChildren = parentChildMap.get(task.parent_id) || []
        parentChildren.push(task.task_id)
        parentChildMap.set(task.parent_id, parentChildren)
      }

      // Handle linked_tasks relationships
      taskNode.linked_tasks.forEach(link => {
        if (!taskMap.has(link.task_id)) return // Skip non-existent tasks

        if (link.linkType === 'Parent') {
          // Tight coupling - tasks should be executed sequentially
          const coupledTasks = tightCouplingMap.get(link.task_id) || []
          coupledTasks.push(task.task_id)
          tightCouplingMap.set(link.task_id, coupledTasks)
        } else if (link.linkType === 'Requires') {
          // Dependency - current task depends on linked task
          const dependencies = dependencyMap.get(task.task_id) || []
          dependencies.push(link.task_id)
          dependencyMap.set(task.task_id, dependencies)

          // Track incoming dependencies
          const incoming = incomingDependencies.get(link.task_id) || []
          incoming.push(task.task_id)
          incomingDependencies.set(link.task_id, incoming)
        }
      })
    })

    return {
      taskMap,
      parentChildMap,
      dependencyMap,
      tightCouplingMap,
      incomingDependencies
    }
  }

  /**
   * Calculates hierarchical levels using topological sorting
   * @param {Object} maps - Relationship maps from buildRelationshipMaps
   * @returns {Map} task_id -> level mapping
   */
  const calculateLevels = (maps) => {
    const { taskMap, parentChildMap, dependencyMap, tightCouplingMap } = maps
    const levels = new Map()
    const visited = new Set()
    const visiting = new Set() // For cycle detection

    const calculateLevel = (taskId) => {
      if (levels.has(taskId)) return levels.get(taskId)
      if (visiting.has(taskId)) {
        console.warn(`Cycle detected involving task: ${taskId}`)
        return 0 // Break cycle with default level
      }

      visiting.add(taskId)
      let maxLevel = 0

      // Level based on parent_id relationship
      const task = taskMap.get(taskId)
      if (task && task.parent_id && taskMap.has(task.parent_id)) {
        const parentLevel = calculateLevel(task.parent_id)
        maxLevel = Math.max(maxLevel, parentLevel + 1)
      }

      // Level based on dependencies (Requires relationships)
      const dependencies = dependencyMap.get(taskId) || []
      dependencies.forEach(depId => {
        if (taskMap.has(depId)) {
          const depLevel = calculateLevel(depId)
          maxLevel = Math.max(maxLevel, depLevel + 1)
        }
      })

      // Level based on tight coupling (Parent relationships)
      // For Parent linkType, tasks should be at the same level as their parent
      if (task && task.linked_tasks) {
        task.linked_tasks.forEach(link => {
          if (link.linkType === 'Parent' && taskMap.has(link.task_id)) {
            const parentLevel = calculateLevel(link.task_id)
            maxLevel = Math.max(maxLevel, parentLevel) // Same level, not +1
          }
        })
      }

      visiting.delete(taskId)
      levels.set(taskId, maxLevel)
      return maxLevel
    }

    // Calculate levels for all tasks
    taskMap.forEach((task, taskId) => {
      if (!levels.has(taskId)) {
        calculateLevel(taskId)
      }
    })

    return levels
  }

  /**
   * Organizes tasks into hierarchical structure with proper ordering
   * @param {Array} allTasks - Array of task objects
   * @returns {Array} Ordered array of tasks with level information
   */
  const getHierarchicalTasks = (allTasks) => {
    if (!allTasks || allTasks.length === 0) {
      return []
    }

    try {
      // Build relationship maps
      const maps = buildRelationshipMaps(allTasks)
      const { taskMap, tightCouplingMap } = maps

      // Calculate hierarchical levels
      const levels = calculateLevels(maps)

      // Create ordered list with levels
      const orderedTasks = []
      const processed = new Set()

      // Group tasks by level
      const tasksByLevel = new Map()
      levels.forEach((level, taskId) => {
        if (!tasksByLevel.has(level)) {
          tasksByLevel.set(level, [])
        }
        tasksByLevel.get(level).push(taskId)
      })

      // Process tasks level by level
      const sortedLevels = Array.from(tasksByLevel.keys()).sort((a, b) => a - b)

      sortedLevels.forEach(level => {
        const tasksAtLevel = tasksByLevel.get(level) || []

        // Handle tight coupling within the same level
        const remainingTasks = new Set(tasksAtLevel)

        tasksAtLevel.forEach(taskId => {
          if (processed.has(taskId)) return

          // Check for tight coupling chains starting from this task
          const couplingChain = buildCouplingChain(taskId, tightCouplingMap, remainingTasks)

          couplingChain.forEach(chainTaskId => {
            if (!processed.has(chainTaskId)) {
              const task = taskMap.get(chainTaskId)
              if (task) {
                orderedTasks.push({
                  ...task,
                  level: levels.get(chainTaskId) || 0
                })
                processed.add(chainTaskId)
                remainingTasks.delete(chainTaskId)
              }
            }
          })
        })
      })

      // Add any remaining unprocessed tasks (shouldn't happen with correct algorithm)
      taskMap.forEach((task, taskId) => {
        if (!processed.has(taskId)) {
          console.warn(`Task ${taskId} was not processed in main algorithm, adding as root`)
          orderedTasks.push({
            ...task,
            level: levels.get(taskId) || 0
          })
        }
      })

      return orderedTasks

    } catch (error) {
      console.error('Error in getHierarchicalTasks:', error)
      // Fallback: return tasks with basic level 0
      return allTasks.map(task => ({ ...task, level: 0 }))
    }
  }

  /**
   * Builds a chain of tightly coupled tasks
   * @param {string} startTaskId - Starting task ID
   * @param {Map} tightCouplingMap - Tight coupling relationships
   * @param {Set} availableTasks - Set of available tasks to process
   * @returns {Array} Ordered chain of tightly coupled task IDs
   */
  const buildCouplingChain = (startTaskId, tightCouplingMap, availableTasks) => {
    const chain = []
    const visited = new Set()

    const addToChain = (taskId) => {
      if (visited.has(taskId) || !availableTasks.has(taskId)) return

      visited.add(taskId)
      chain.push(taskId)

      // Add tightly coupled tasks
      const coupledTasks = tightCouplingMap.get(taskId) || []
      coupledTasks.forEach(coupledId => {
        if (availableTasks.has(coupledId)) {
          addToChain(coupledId)
        }
      })
    }

    addToChain(startTaskId)
    return chain
  }

  return {
    getHierarchicalTasks,
    normalizeLinkedTasks, // Export for testing
    inferLinkType, // Export for testing
    buildRelationshipMaps, // Export for testing
    calculateLevels // Export for testing
  }
}

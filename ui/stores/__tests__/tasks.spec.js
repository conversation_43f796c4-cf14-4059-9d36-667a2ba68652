import { setActive<PERSON>inia, create<PERSON><PERSON> } from 'pinia';
import { useTasksStore } from '../tasks';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the httpClient
vi.mock('../../utils/httpClient.js', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
  },
}));

// Import the mocked httpClient
import httpClient from '../../utils/httpClient.js';

describe('Tasks Store', () => {
  beforeEach(() => {
    // Create a new Pinia instance and make it active for each test
    setActivePinia(createPinia());
    // Reset mocks before each test
    vi.clearAllMocks();
  });

  it('should initialize with correct defaults', () => {
    const store = useTasksStore();
    expect(store.tasks).toEqual([]);
    expect(store.loading).toBe(false);
    expect(store.error).toBeNull();
    expect(store.uploadProgress).toBe(0);
    expect(store.uploadStatus).toBe('idle');
    expect(store.lastUploadResult).toBeNull();
  });

  // More tests will be added here for CRUD operations

  describe('addTask', () => {
    it('should add a task successfully', async () => {
      const store = useTasksStore();
      const newTaskData = { summary: 'New Task', description: 'A new task to test', project_id: 'proj-123' };
      const createdTask = { id: 'task-1', task_id: 'TSK-001', ...newTaskData, status: 'Backlog' };

      // Mock httpClient.post to return the created task
      httpClient.post.mockResolvedValue({
        success: true,
        data: { task: createdTask },
        message: 'Task created successfully'
      });

      const result = await store.addTask(newTaskData);

      expect(httpClient.post).toHaveBeenCalledTimes(1);
      expect(httpClient.post).toHaveBeenCalledWith('/tasks', expect.objectContaining({
        summary: newTaskData.summary,
        description: newTaskData.description,
        project_id: newTaskData.project_id,
      }));
      expect(store.tasks).toContainEqual(createdTask);
      expect(store.error).toBeNull();
      expect(result).toEqual(createdTask);
    });

    it('should handle errors when adding a task', async () => {
      const store = useTasksStore();
      const newTaskData = { summary: 'Error Task', project_id: 'proj-123' };
      const errorMessage = 'Failed to add task';

      httpClient.post.mockRejectedValue(new Error(errorMessage));

      // The action re-throws the error, so we test for that behavior.
      // Also, the error should be set in the store.
      let thrownError = null;
      try {
        await store.addTask(newTaskData);
      } catch (e) {
        thrownError = e;
      }

      expect(httpClient.post).toHaveBeenCalledTimes(1);
      expect(store.tasks).not.toContainEqual(expect.objectContaining({ summary: 'Error Task' }));
      expect(store.error).toBe(`Failed to add task: ${errorMessage}`);
      expect(thrownError).toBeInstanceOf(Error);
      expect(thrownError.message).toBe(errorMessage);
    });

    it('should generate a task_id if not provided', async () => {
      const store = useTasksStore();
      const newTaskData = { summary: 'Task without ID', project_id: 'proj-123' };
      const createdTask = { id: 'task-2', task_id: expect.stringMatching(/^TASK-\d+-[A-Z0-9]+$/), ...newTaskData, status: 'Backlog' };

      httpClient.post.mockResolvedValue({
        success: true,
        data: { task: createdTask },
        message: 'Task created successfully'
      });

      await store.addTask(newTaskData);

      expect(httpClient.post).toHaveBeenCalledWith('/tasks', expect.objectContaining({
        task_id: expect.stringMatching(/^TASK-\d+-[A-Z0-9]+$/),
        summary: newTaskData.summary,
        project_id: newTaskData.project_id,
      }));
    });

     it('should use provided task_id if valid', async () => {
      const store = useTasksStore();
      const newTaskData = { task_id: 'MY-TASK-1', summary: 'Task with ID', project_id: 'proj-123' };
      const createdTask = { id: 'task-3', ...newTaskData, status: 'Backlog' };

      httpClient.post.mockResolvedValue({
        success: true,
        data: { task: createdTask },
        message: 'Task created successfully'
      });
      // Ensure tasks is empty so checkTaskIdExists passes for 'MY-TASK-1'
      store.tasks = [];

      await store.addTask(newTaskData);

      expect(httpClient.post).toHaveBeenCalledWith('/tasks', expect.objectContaining({
        task_id: 'MY-TASK-1',
        summary: newTaskData.summary,
        project_id: newTaskData.project_id,
      }));
    });

    it('should throw error for invalid task_id format', async () => {
        const store = useTasksStore();
        const newTaskData = { task_id: 'invalid!', summary: 'Task with invalid ID', project_id: 'proj-123' };

        let thrownError = null;
        try {
            await store.addTask(newTaskData);
        } catch (e) {
            thrownError = e;
        }

        expect(httpClient.post).not.toHaveBeenCalled();
        // The error message in the store includes the specific validation rule broken.
        expect(store.error).toBe('Failed to add task: Task ID can only contain letters, numbers, and hyphens (cannot start or end with a hyphen unless it is a single character).');
        expect(thrownError).toBeInstanceOf(Error);
        expect(thrownError.message).toContain('Task ID can only contain letters, numbers, and hyphens');
    });

    it('should throw error if task_id already exists', async () => {
        const store = useTasksStore();
        const existingTaskId = 'EXISTING-TASK';
        const newTaskData = { task_id: existingTaskId, summary: 'Task with existing ID', project_id: 'proj-123' };

        // Pre-populate tasks to make the checkTaskIdExists find the ID
        store.tasks = [{ task_id: existingTaskId, id: 'pb-existing', summary: 'An existing task' }];

        let thrownError = null;
        try {
            await store.addTask(newTaskData);
        } catch (e) {
            thrownError = e;
        }

        expect(httpClient.post).not.toHaveBeenCalled();
        expect(store.error).toBe(`Failed to add task: Task ID "${existingTaskId}" already exists.`);
        expect(thrownError).toBeInstanceOf(Error);
        expect(thrownError.message).toContain(`Task ID "${existingTaskId}" already exists`);
    });
  });

  describe('fetchTasks', () => {
    it('should fetch tasks successfully and populate the store', async () => {
      const store = useTasksStore();
      const mockTasks = [
        { id: '1', task_id: 'TSK-001', summary: 'Task 1' },
        { id: '2', task_id: 'TSK-002', summary: 'Task 2' },
      ];
      httpClient.get.mockResolvedValue({
        success: true,
        data: { tasks: { tasks: mockTasks } },
        message: 'Tasks fetched successfully'
      });

      await store.fetchTasks();

      expect(httpClient.get).toHaveBeenCalledTimes(1);
      expect(httpClient.get).toHaveBeenCalledWith('/tasks', { params: {} });
      expect(store.tasks).toEqual(mockTasks);
      expect(store.loading).toBe(false);
      expect(store.error).toBeNull();
    });

    it('should set loading to true while fetching', async () => {
      const store = useTasksStore();
      httpClient.get.mockImplementation(() => {
        expect(store.loading).toBe(true);
        return Promise.resolve({
          success: true,
          data: { tasks: { tasks: [] } },
          message: 'Tasks fetched successfully'
        });
      });
      await store.fetchTasks();
    });

    it('should handle errors when fetching tasks', async () => {
      const store = useTasksStore();
      const errorMessage = 'Failed to fetch';
      httpClient.get.mockRejectedValue(new Error(errorMessage));

      // The fetchTasks method throws the error after setting the error state
      await expect(store.fetchTasks()).rejects.toThrow(errorMessage);

      expect(store.loading).toBe(false);
      expect(store.tasks).toEqual([]);
      expect(store.error).toBe(`Failed to fetch tasks: ${errorMessage}`);
    });

    it('should pass filters to httpClient.get', async () => {
      const store = useTasksStore();
      const filters = { status: 'Open', priority: 'High' };
      httpClient.get.mockResolvedValue({
        success: true,
        data: { tasks: { tasks: [] } },
        message: 'Tasks fetched successfully'
      });

      await store.fetchTasks(filters);

      expect(httpClient.get).toHaveBeenCalledWith('/tasks', { params: filters });
    });
  });

  describe('updateTask', () => {
    it('should update a task successfully', async () => {
      const store = useTasksStore();
      const initialTask = { id: 'pb-id-1', task_id: 'TSK-001', summary: 'Old Summary', status: 'Open' };
      store.tasks = [initialTask]; // Set initial state

      const updates = { summary: 'New Summary', status: 'Closed' };
      const updatedTaskFromDb = { ...initialTask, ...updates };

      httpClient.put.mockResolvedValue({
        success: true,
        data: { task: updatedTaskFromDb },
        message: 'Task updated successfully'
      });

      const result = await store.updateTask('TSK-001', updates);

      expect(httpClient.put).toHaveBeenCalledTimes(1);
      expect(httpClient.put).toHaveBeenCalledWith('/tasks/TSK-001', updates);
      expect(store.tasks[0]).toEqual(expect.objectContaining(updatedTaskFromDb));
      expect(store.error).toBeNull();
      expect(result).toEqual(updatedTaskFromDb);
    });

    it('should handle task not found when updating', async () => {
      const store = useTasksStore();
      const updates = { summary: 'New Summary' };

      // httpClient.put should not be called if task not in store
      // The store's updateTask returns false if task not found in local state
      const result = await store.updateTask('NON-EXISTENT-ID', updates);

      expect(httpClient.put).not.toHaveBeenCalled();
      expect(store.error).toBeNull(); // Or specific error if implemented
      expect(result).toBe(false);
    });

    it('should handle errors from httpClient when updating a task', async () => {
      const store = useTasksStore();
      const initialTask = { id: 'pb-id-1', task_id: 'TSK-001', summary: 'Old Summary' };
      store.tasks = [initialTask];
      const updates = { summary: 'New Summary' };
      const errorMessage = 'Failed to update';

      httpClient.put.mockRejectedValue(new Error(errorMessage));

      let thrownError = null;
      try {
        await store.updateTask('TSK-001', updates);
      } catch (e) {
        thrownError = e;
      }

      expect(httpClient.put).toHaveBeenCalledWith('/tasks/TSK-001', updates);
      expect(store.tasks[0]).toEqual(initialTask); // Task should not be updated in store
      expect(store.error).toBe(`Failed to update task: ${errorMessage}`);
      expect(thrownError).toBeInstanceOf(Error);
      expect(thrownError.message).toBe(errorMessage);
    });
     it('should update a task successfully using PocketBase ID if task_id not found', async () => {
      const store = useTasksStore();
      const initialTask = { id: 'pb-id-xyz', task_id: 'TSK-XYZ', summary: 'Old Summary', status: 'Open' };
      store.tasks = [initialTask];

      const updates = { summary: 'New Summary XYZ', status: 'Pending' };
      const updatedTaskFromDb = { ...initialTask, ...updates };

      httpClient.put.mockResolvedValue({
        success: true,
        data: { task: updatedTaskFromDb },
        message: 'Task updated successfully'
      });

      // Call updateTask with the PocketBase ID
      const result = await store.updateTask(initialTask.id, updates);

      expect(httpClient.put).toHaveBeenCalledTimes(1);
      expect(httpClient.put).toHaveBeenCalledWith(`/tasks/${initialTask.id}`, updates);
      expect(store.tasks[0]).toEqual(expect.objectContaining(updatedTaskFromDb));
      expect(store.error).toBeNull();
      expect(result).toEqual(updatedTaskFromDb);
    });
  });

  describe('deleteTask', () => {
    it('should delete a task successfully', async () => {
      const store = useTasksStore();
      const taskToDelete = { id: 'pb-id-1', task_id: 'TSK-001', summary: 'Task to delete' };
      store.tasks = [taskToDelete, { id: 'pb-id-2', task_id: 'TSK-002', summary: 'Another task' }];

      httpClient.delete.mockResolvedValue({
        success: true,
        message: 'Task deleted successfully'
      });

      const result = await store.deleteTask('TSK-001');

      expect(httpClient.delete).toHaveBeenCalledTimes(1);
      expect(httpClient.delete).toHaveBeenCalledWith('/tasks/TSK-001');
      expect(store.tasks.find(t => t.task_id === 'TSK-001')).toBeUndefined();
      expect(store.tasks.length).toBe(1);
      expect(store.error).toBeNull();
      expect(result).toBe(true);
    });

    it('should handle task not found when deleting', async () => {
      const store = useTasksStore();
      store.tasks = [{ id: 'pb-id-1', task_id: 'TSK-001', summary: 'Existing task' }];

      const result = await store.deleteTask('NON-EXISTENT-ID');

      expect(httpClient.delete).not.toHaveBeenCalled();
      expect(store.error).toBeNull(); // Or specific error
      expect(result).toBe(false);
      expect(store.tasks.length).toBe(1);
    });

    it('should handle errors from httpClient when deleting a task', async () => {
      const store = useTasksStore();
      const taskToDelete = { id: 'pb-id-1', task_id: 'TSK-001', summary: 'Task to delete' };
      store.tasks = [taskToDelete];
      const errorMessage = 'Failed to delete';

      httpClient.delete.mockRejectedValue(new Error(errorMessage));

      let thrownError = null;
      try {
        await store.deleteTask('TSK-001');
      } catch (e) {
        thrownError = e;
      }

      expect(httpClient.delete).toHaveBeenCalledWith('/tasks/TSK-001');
      expect(store.tasks.find(t => t.task_id === 'TSK-001')).toBeDefined(); // Task should still be in store
      expect(store.error).toBe(`Failed to delete task: ${errorMessage}`);
      expect(thrownError).toBeInstanceOf(Error);
      expect(thrownError.message).toBe(errorMessage);
    });
     it('should delete a task successfully using PocketBase ID if task_id not found', async () => {
      const store = useTasksStore();
      const taskToDelete = { id: 'pb-id-abc', task_id: 'TSK-ABC', summary: 'Task to delete via PB ID' };
      store.tasks = [taskToDelete, { id: 'pb-id-2', task_id: 'TSK-002', summary: 'Another task' }];

      httpClient.delete.mockResolvedValue({
        success: true,
        message: 'Task deleted successfully'
      });

      const result = await store.deleteTask(taskToDelete.id); // Use PocketBase ID

      expect(httpClient.delete).toHaveBeenCalledTimes(1);
      expect(httpClient.delete).toHaveBeenCalledWith(`/tasks/${taskToDelete.id}`);
      expect(store.tasks.find(t => t.id === taskToDelete.id)).toBeUndefined();
      expect(store.tasks.length).toBe(1);
      expect(store.error).toBeNull();
      expect(result).toBe(true);
    });
  });

  describe('getTaskById', () => {
    it('should get a task by its original ID successfully', async () => {
      const store = useTasksStore();
      const mockTask = { id: 'pb-id-1', task_id: 'TSK-001', summary: 'Fetched Task' };

      httpClient.get.mockResolvedValue({
        success: true,
        data: { task: mockTask },
        message: 'Task fetched successfully'
      });

      const result = await store.getTaskById('TSK-001');

      expect(httpClient.get).toHaveBeenCalledTimes(1);
      expect(httpClient.get).toHaveBeenCalledWith('/tasks/TSK-001');
      expect(result).toEqual(mockTask);
      expect(store.error).toBeNull();
    });

    it('should return null if task is not found by original ID', async () => {
      const store = useTasksStore();
      httpClient.get.mockResolvedValue({
        success: true,
        data: { task: null },
        message: 'Task not found'
      });

      const result = await store.getTaskById('NON-EXISTENT-ID');

      expect(httpClient.get).toHaveBeenCalledWith('/tasks/NON-EXISTENT-ID');
      expect(result).toBeNull();
      expect(store.error).toBeNull(); // Or specific error if desired for not found
    });

    it('should handle errors from httpClient when getting a task by original ID', async () => {
      const store = useTasksStore();
      const errorMessage = 'Failed to fetch by original ID';
      httpClient.get.mockRejectedValue(new Error(errorMessage));

      // Store the promise to await it and catch the expected throw
      let thrownError = null;
      try {
        await store.getTaskById('ERROR-ID');
      } catch (e) {
        thrownError = e;
      }

      expect(httpClient.get).toHaveBeenCalledWith('/tasks/ERROR-ID');
      expect(store.error).toBe(`Failed to fetch task: ${errorMessage}`);
      // The action re-throws the error, so we check that
      expect(thrownError).toBeInstanceOf(Error);
      expect(thrownError.message).toBe(errorMessage);
    });
  });
});

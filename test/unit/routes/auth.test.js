/**
 * Authentication Routes Unit Tests
 * Tests for authentication API endpoints
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import request from 'supertest'
import express from 'express'
import { authRoutes } from '../../../api/routes/auth.js'
import { AuthService } from '../../../api/services/authService.js'
import { createApp } from '../../../api/config/server.js'

// Ensure NODE_ENV is set to test
process.env.NODE_ENV = 'test'

// Mock the AuthService
vi.mock('../../../api/services/authService.js', () => ({
  AuthService: {
    login: vi.fn(),
    register: vi.fn(),
    logout: vi.fn(),
    refreshToken: vi.fn(),
    requestPasswordReset: vi.fn(),
    resetPassword: vi.fn(),
    getCurrentUser: vi.fn(),
    updateProfile: vi.fn(),
    changePassword: vi.fn()
  }
}))

// Mock authentication middleware
vi.mock('../../../api/middleware/auth.js', () => ({
  authenticate: (req, res, next) => {
    req.user = { id: 'test-user-id', email: '<EMAIL>' }
    next()
  },
  optionalAuthenticate: (req, res, next) => next()
}))

// Mock CSRF protection middleware
vi.mock('../../../api/middleware/csrf.js', () => ({
  csrfProtection: (req, res, next) => next(),
  generateCSRFToken: (req, res, next) => {
    req.csrfToken = 'test-csrf-token'
    next()
  },
  csrfTokenEndpoint: (req, res) => {
    res.json({
      success: true,
      data: { csrfToken: 'test-csrf-token' }
    })
  }
}))

// Mock validation middleware
vi.mock('../../../api/utils/validation.js', () => ({
  authValidationRules: {
    login: [(req, res, next) => {
      // Simulate validation failure for invalid email
      if (req.body.email && !req.body.email.includes('@')) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: [{ field: 'email', message: 'Must be a valid email address' }]
          }
        })
      }
      // Simulate validation failure for missing password
      if (!req.body.password) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: [{ field: 'password', message: 'Password is required' }]
          }
        })
      }
      next()
    }],
    register: [(req, res, next) => {
      // Simulate validation failure for weak password
      if (req.body.password && req.body.password.length < 8) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: [{ field: 'password', message: 'Password must be at least 8 characters long' }]
          }
        })
      }
      // Simulate validation failure for password mismatch
      if (req.body.password && req.body.passwordConfirm && req.body.password !== req.body.passwordConfirm) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: [{ field: 'passwordConfirm', message: 'Passwords do not match' }]
          }
        })
      }
      next()
    }],
    refresh: [(req, res, next) => {
      if (!req.body.refreshToken) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: [{ field: 'refreshToken', message: 'Refresh token is required' }]
          }
        })
      }
      next()
    }],
    forgotPassword: [(req, res, next) => {
      if (req.body.email && !req.body.email.includes('@')) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: [{ field: 'email', message: 'Must be a valid email address' }]
          }
        })
      }
      next()
    }],
    resetPassword: [(req, res, next) => {
      if (!req.body.token) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: [{ field: 'token', message: 'Reset token is required' }]
          }
        })
      }
      next()
    }],
    updateProfile: [(req, res, next) => {
      if (req.body.email && !req.body.email.includes('@')) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: [{ field: 'email', message: 'Must be a valid email address' }]
          }
        })
      }
      next()
    }],
    changePassword: [(req, res, next) => {
      if (req.body.newPassword && req.body.newPasswordConfirm && req.body.newPassword !== req.body.newPasswordConfirm) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Validation failed',
            details: [{ field: 'newPasswordConfirm', message: 'Passwords do not match' }]
          }
        })
      }
      next()
    }]
  }
}))

// Mock password reset middleware
vi.mock('../../../api/middleware/passwordReset.js', () => ({
  passwordResetRateLimit: (req, res, next) => next(),
  emailResetRateLimit: (req, res, next) => next(),
  progressiveDelayMiddleware: (req, res, next) => next(),
  validateResetTokenMiddleware: (req, res, next) => next()
}))

describe('Authentication Routes', () => {
  let app

  beforeEach(() => {
    app = express()
    app.use(express.json())
    app.use('/api/auth', authRoutes)

    // Clear all mocks
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('POST /api/auth/login', () => {
    it('should login user with valid credentials', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user'
      }

      AuthService.login.mockResolvedValue({
        success: true,
        user: mockUser,
        token: 'jwt-token',
        refreshToken: 'refresh-token'
      })

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
          rememberMe: true
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toEqual(mockUser)
      expect(response.body.data.token).toBe('jwt-token')
      expect(response.body.message).toBe('Login successful')
      expect(AuthService.login).toHaveBeenCalledWith('<EMAIL>', 'password123', true, '', '::ffff:127.0.0.1')
    })

    it('should return validation error for invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: 'invalid-email',
          password: 'password123'
        })

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })

    it('should return validation error for missing password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>'
        })

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('POST /api/auth/register', () => {
    it('should register user with valid data', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        verified: false
      }

      AuthService.register.mockResolvedValue({
        success: true,
        user: mockUser
      })

      const response = await request(app)
        .post('/api/auth/register')
        .send({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'Password123',
          passwordConfirm: 'Password123'
        })

      expect(response.status).toBe(201)
      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toEqual(mockUser)
      expect(response.body.message).toContain('Registration successful')
      expect(AuthService.register).toHaveBeenCalledWith({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123',
        passwordConfirm: 'Password123'
      }, expect.any(Object))
    })

    it('should return validation error for weak password', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'weak',
          passwordConfirm: 'weak'
        })

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })

    it('should return validation error for password mismatch', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'Password123',
          passwordConfirm: 'DifferentPassword123'
        })

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('POST /api/auth/logout', () => {
    it('should logout user successfully', async () => {
      AuthService.logout.mockResolvedValue({
        success: true,
        message: 'Logout successful'
      })

      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', 'Bearer jwt-token')

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Logout successful')
      expect(AuthService.logout).toHaveBeenCalledWith('jwt-token', expect.any(Object))
    })
  })

  describe('POST /api/auth/refresh', () => {
    it('should refresh token successfully', async () => {
      AuthService.refreshToken.mockResolvedValue({
        success: true,
        token: 'new-jwt-token',
        refreshToken: 'new-refresh-token'
      })

      const response = await request(app)
        .post('/api/auth/refresh')
        .send({
          refreshToken: 'refresh-token'
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.token).toBe('new-jwt-token')
      expect(response.body.data.refreshToken).toBe('new-refresh-token')
      expect(AuthService.refreshToken).toHaveBeenCalledWith('refresh-token', expect.any(Object))
    })

    it('should return validation error for missing refresh token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({})

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('POST /api/auth/forgot-password', () => {
    it('should send password reset email', async () => {
      AuthService.requestPasswordReset.mockResolvedValue({
        success: true,
        message: 'Password reset link sent to your email'
      })

      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({
          email: '<EMAIL>'
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.message).toContain('Password reset link sent')
      expect(AuthService.requestPasswordReset).toHaveBeenCalledWith('<EMAIL>', expect.any(Object))
    })

    it('should return validation error for invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/forgot-password')
        .send({
          email: 'invalid-email'
        })

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('POST /api/auth/reset-password', () => {
    it('should reset password successfully', async () => {
      AuthService.resetPassword.mockResolvedValue({
        success: true,
        message: 'Password reset successful'
      })

      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          token: 'reset-token',
          password: 'NewPassword123',
          passwordConfirm: 'NewPassword123'
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Password reset successful')
      expect(AuthService.resetPassword).toHaveBeenCalledWith('reset-token', 'NewPassword123', 'NewPassword123', expect.any(Object))
    })

    it('should return validation error for missing token', async () => {
      const response = await request(app)
        .post('/api/auth/reset-password')
        .send({
          password: 'NewPassword123',
          passwordConfirm: 'NewPassword123'
        })

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('GET /api/auth/me', () => {
    it('should get current user profile', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
        role: 'user'
      }

      AuthService.getCurrentUser.mockResolvedValue({
        success: true,
        user: mockUser
      })

      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer jwt-token')

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toEqual(mockUser)
      expect(AuthService.getCurrentUser).toHaveBeenCalledWith('test-user-id', expect.any(Object))
    })
  })

  describe('PUT /api/auth/profile', () => {
    it('should update user profile', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Updated Name',
        role: 'user'
      }

      AuthService.updateProfile.mockResolvedValue({
        success: true,
        user: mockUser
      })

      const response = await request(app)
        .put('/api/auth/profile')
        .set('Authorization', 'Bearer jwt-token')
        .send({
          name: 'Updated Name',
          email: '<EMAIL>'
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.data.user).toEqual(mockUser)
      expect(response.body.message).toBe('Profile updated successfully')
      expect(AuthService.updateProfile).toHaveBeenCalledWith('test-user-id', {
        name: 'Updated Name',
        email: '<EMAIL>',
        avatar: undefined
      }, expect.any(Object))
    })

    it('should return validation error for invalid email', async () => {
      const response = await request(app)
        .put('/api/auth/profile')
        .set('Authorization', 'Bearer jwt-token')
        .send({
          email: 'invalid-email'
        })

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('PUT /api/auth/password', () => {
    it('should change password successfully', async () => {
      AuthService.changePassword.mockResolvedValue({
        success: true,
        message: 'Password changed successfully'
      })

      const response = await request(app)
        .put('/api/auth/password')
        .set('Authorization', 'Bearer jwt-token')
        .send({
          currentPassword: 'oldPassword123',
          newPassword: 'NewPassword123',
          newPasswordConfirm: 'NewPassword123'
        })

      expect(response.status).toBe(200)
      expect(response.body.success).toBe(true)
      expect(response.body.message).toBe('Password changed successfully')
      expect(AuthService.changePassword).toHaveBeenCalledWith(
        'test-user-id',
        'oldPassword123',
        'NewPassword123',
        'NewPassword123',
        expect.any(Object)
      )
    })

    it('should return validation error for password mismatch', async () => {
      const response = await request(app)
        .put('/api/auth/password')
        .set('Authorization', 'Bearer jwt-token')
        .send({
          currentPassword: 'oldPassword123',
          newPassword: 'NewPassword123',
          newPasswordConfirm: 'DifferentPassword123'
        })

      expect(response.status).toBe(400)
      expect(response.body.success).toBe(false)
      expect(response.body.error.code).toBe('VALIDATION_ERROR')
    })
  })
})

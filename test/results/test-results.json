{"numTotalTestSuites": 175, "numPassedTestSuites": 175, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 342, "numPassedTests": 342, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1753905128085, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should complete full user registration flow", "status": "passed", "title": "should complete full user registration flow", "duration": 14.467168000001038, "failureMessages": [], "location": {"line": 90, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.7490559999987454, "failureMessages": [], "location": {"line": 117, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Registration Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Registration Journey should handle existing email registration attempt", "status": "passed", "title": "should handle existing email registration attempt", "duration": 0.7517800000005082, "failureMessages": [], "location": {"line": 138, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should complete successful login flow", "status": "passed", "title": "should complete successful login flow", "duration": 0.85889700000007, "failureMessages": [], "location": {"line": 161, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should handle invalid login credentials", "status": "passed", "title": "should handle invalid login credentials", "duration": 3.0656770000005054, "failureMessages": [], "location": {"line": 184, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Login <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Login Journey should redirect to intended page after login", "status": "passed", "title": "should redirect to intended page after login", "duration": 10.830244999999195, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "User Lo<PERSON>ut <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) User Logout Journey should complete logout flow", "status": "passed", "title": "should complete logout flow", "duration": 0.6577550000001793, "failureMessages": [], "location": {"line": 223, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 0.47463299999981245, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Password Reset Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Password Reset Journey should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 0.5884779999996681, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 0.5266349999983504, "failureMessages": [], "location": {"line": 285, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Profile Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Profile Management Journey should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 0.4381329999996524, "failureMessages": [], "location": {"line": 303, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle session timeout gracefully", "status": "passed", "title": "should handle session timeout gracefully", "duration": 0.3803619999998773, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Session Management Journey"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Session Management Journey should handle concurrent sessions", "status": "passed", "title": "should handle concurrent sessions", "duration": 0.3102019999987533, "failureMessages": [], "location": {"line": 347, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle network errors gracefully", "status": "passed", "title": "should handle network errors gracefully", "duration": 0.8282480000016221, "failureMessages": [], "location": {"line": 367, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication User Journeys E2E Tests (TEST-008)", "Error <PERSON>"], "fullName": "Authentication User Journeys E2E Tests (TEST-008) Error Handling Journey should handle server errors gracefully", "status": "passed", "title": "should handle server errors gracefully", "duration": 0.3737569999993866, "failureMessages": [], "location": {"line": 388, "column": 7}, "meta": {}}], "startTime": 1753905138667, "endTime": 1753905138703.3738, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/e2e/auth-journeys.test.js"}, {"assertionResults": [{"ancestorTitles": ["Complete Authentication Flow Integration Tests", "CSRF Token Endpoint"], "fullName": "Complete Authentication Flow Integration Tests CSRF Token Endpoint should provide CSRF token via GET /api/auth/csrf-token", "status": "passed", "title": "should provide CSRF token via GET /api/auth/csrf-token", "duration": 221.73034000000007, "failureMessages": [], "location": {"line": 174, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "CSRF Token Endpoint"], "fullName": "Complete Authentication Flow Integration Tests CSRF Token Endpoint should return same token for same session", "status": "passed", "title": "should return same token for same session", "duration": 24.380935999999565, "failureMessages": [], "location": {"line": 193, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Complete Authentication Flow"], "fullName": "Complete Authentication Flow Integration Tests Complete Authentication Flow should complete full authentication workflow", "status": "passed", "title": "should complete full authentication workflow", "duration": 189.694348, "failureMessages": [], "location": {"line": 216, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Complete Authentication Flow"], "fullName": "Complete Authentication Flow Integration Tests Complete Authentication Flow should reject login with invalid CSRF token", "status": "passed", "title": "should reject login with invalid CSRF token", "duration": 9.449889999999868, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Complete Authentication Flow"], "fullName": "Complete Authentication Flow Integration Tests Complete Authentication Flow should reject login without CSRF token", "status": "passed", "title": "should reject login without CSRF token", "duration": 9.074829000000136, "failureMessages": [], "location": {"line": 280, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Token Response Format"], "fullName": "Complete Authentication Flow Integration Tests Token Response Format should return properly formatted authentication token", "status": "passed", "title": "should return properly formatted authentication token", "duration": 10.663196000000426, "failureMessages": [], "location": {"line": 296, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Erro<PERSON>"], "fullName": "Complete Authentication Flow Integration Tests Error Handling should provide clear error messages for CSRF failures", "status": "passed", "title": "should provide clear error messages for CSRF failures", "duration": 9.652662999999848, "failureMessages": [], "location": {"line": 332, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Erro<PERSON>"], "fullName": "Complete Authentication Flow Integration Tests Error Handling should handle CSRF token endpoint errors gracefully", "status": "passed", "title": "should handle CSRF token endpoint errors gracefully", "duration": 4.055782999999792, "failureMessages": [], "location": {"line": 364, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Session Management Integration"], "fullName": "Complete Authentication Flow Integration Tests Session Management Integration should handle session extension with proper authentication", "status": "passed", "title": "should handle session extension with proper authentication", "duration": 61.784310000000005, "failureMessages": [], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["Complete Authentication Flow Integration Tests", "Session Management Integration"], "fullName": "Complete Authentication Flow Integration Tests Session Management Integration should get session status with proper authentication", "status": "passed", "title": "should get session status with proper authentication", "duration": 27.140701999999692, "failureMessages": [], "location": {"line": 410, "column": 7}, "meta": {}}], "startTime": 1753905132970, "endTime": 1753905133539.1406, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-flow-complete.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should complete full login flow successfully", "status": "passed", "title": "should complete full login flow successfully", "duration": 35.027376000000004, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle login failure gracefully", "status": "passed", "title": "should handle login failure gracefully", "duration": 106.7181359999995, "failureMessages": [], "location": {"line": 110, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Login Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Login Flow should handle network errors during login", "status": "passed", "title": "should handle network errors during login", "duration": 6.6063389999999345, "failureMessages": [], "location": {"line": 138, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should complete full registration flow successfully", "status": "passed", "title": "should complete full registration flow successfully", "duration": 30.948284000000058, "failureMessages": [], "location": {"line": 159, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Registration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Registration Flow should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 1.2291710000008607, "failureMessages": [], "location": {"line": 183, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should complete full logout flow successfully", "status": "passed", "title": "should complete full logout flow successfully", "duration": 1.6648500000010245, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Complete Logout Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Complete Logout Flow should handle logout errors gracefully", "status": "passed", "title": "should handle logout errors gracefully", "duration": 3.007582000000184, "failureMessages": [], "location": {"line": 234, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset request flow", "status": "passed", "title": "should complete password reset request flow", "duration": 5.290727999999945, "failureMessages": [], "location": {"line": 258, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Password Reset Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Password Reset Flow should complete password reset confirmation flow", "status": "passed", "title": "should complete password reset confirmation flow", "duration": 3.014440000000832, "failureMessages": [], "location": {"line": 274, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should restore session from localStorage on initialization", "status": "passed", "title": "should restore session from localStorage on initialization", "duration": 1.7031889999998384, "failureMessages": [], "location": {"line": 292, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Session Restoration Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Session Restoration Flow should handle invalid stored session gracefully", "status": "passed", "title": "should handle invalid stored session gracefully", "duration": 22.924673999999868, "failureMessages": [], "location": {"line": 319, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete profile update flow", "status": "passed", "title": "should complete profile update flow", "duration": 1.0267930000009073, "failureMessages": [], "location": {"line": 346, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Flows Integration Tests (TEST-005)", "Profile Management Flow"], "fullName": "Authentication Flows Integration Tests (TEST-005) Profile Management Flow should complete password change flow", "status": "passed", "title": "should complete password change flow", "duration": 4.862376999999469, "failureMessages": [], "location": {"line": 368, "column": 7}, "meta": {}}], "startTime": 1753905135921, "endTime": 1753905136147.8623, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-flows.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from / to login", "status": "passed", "title": "should redirect unauthenticated users from / to login", "duration": 10.084254999999757, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /", "status": "passed", "title": "should allow authenticated users to access /", "duration": 1.451218999998673, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /dashboard to login", "status": "passed", "title": "should redirect unauthenticated users from /dashboard to login", "duration": 2.5289899999988847, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /dashboard", "status": "passed", "title": "should allow authenticated users to access /dashboard", "duration": 0.7233159999996133, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /projects to login", "status": "passed", "title": "should redirect unauthenticated users from /projects to login", "duration": 0.5890300000010029, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /projects", "status": "passed", "title": "should allow authenticated users to access /projects", "duration": 1.2664199999999255, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /tasks to login", "status": "passed", "title": "should redirect unauthenticated users from /tasks to login", "duration": 0.7561379999988276, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /tasks", "status": "passed", "title": "should allow authenticated users to access /tasks", "duration": 0.8013530000007449, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /tasks/123 to login", "status": "passed", "title": "should redirect unauthenticated users from /tasks/123 to login", "duration": 0.5559549999998126, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /tasks/123", "status": "passed", "title": "should allow authenticated users to access /tasks/123", "duration": 1.7634460000008403, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /upload to login", "status": "passed", "title": "should redirect unauthenticated users from /upload to login", "duration": 0.6916660000006232, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /upload", "status": "passed", "title": "should allow authenticated users to access /upload", "duration": 0.3659429999988788, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /profile to login", "status": "passed", "title": "should redirect unauthenticated users from /profile to login", "duration": 0.48561700000027486, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /profile", "status": "passed", "title": "should allow authenticated users to access /profile", "duration": 0.29973100000097475, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /settings to login", "status": "passed", "title": "should redirect unauthenticated users from /settings to login", "duration": 0.36728900000161957, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /settings", "status": "passed", "title": "should allow authenticated users to access /settings", "duration": 0.2799720000002708, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should allow unauthenticated users to access /login", "status": "passed", "title": "should allow unauthenticated users to access /login", "duration": 0.37460399999872607, "failureMessages": [], "location": {"line": 144, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /login to dashboard", "status": "passed", "title": "should redirect authenticated users from /login to dashboard", "duration": 0.40155199999935576, "failureMessages": [], "location": {"line": 157, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /login to original destination", "status": "passed", "title": "should redirect authenticated users from /login to original destination", "duration": 1.1171160000012605, "failureMessages": [], "location": {"line": 170, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should allow unauthenticated users to access /register", "status": "passed", "title": "should allow unauthenticated users to access /register", "duration": 0.3450279999997292, "failureMessages": [], "location": {"line": 144, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /register to dashboard", "status": "passed", "title": "should redirect authenticated users from /register to dashboard", "duration": 0.6852419999995618, "failureMessages": [], "location": {"line": 157, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /register to original destination", "status": "passed", "title": "should redirect authenticated users from /register to original destination", "duration": 0.6364410000005591, "failureMessages": [], "location": {"line": 170, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should allow unauthenticated users to access /forgot-password", "status": "passed", "title": "should allow unauthenticated users to access /forgot-password", "duration": 0.28764700000101584, "failureMessages": [], "location": {"line": 144, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /forgot-password to dashboard", "status": "passed", "title": "should redirect authenticated users from /forgot-password to dashboard", "duration": 0.28473900000062713, "failureMessages": [], "location": {"line": 157, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /forgot-password to original destination", "status": "passed", "title": "should redirect authenticated users from /forgot-password to original destination", "duration": 0.40322399999968184, "failureMessages": [], "location": {"line": 170, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should allow unauthenticated users to access /reset-password", "status": "passed", "title": "should allow unauthenticated users to access /reset-password", "duration": 0.28146799999922223, "failureMessages": [], "location": {"line": 144, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /reset-password to dashboard", "status": "passed", "title": "should redirect authenticated users from /reset-password to dashboard", "duration": 57.03004200000032, "failureMessages": [], "location": {"line": 157, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /reset-password to original destination", "status": "passed", "title": "should redirect authenticated users from /reset-password to original destination", "duration": 6.964803999999276, "failureMessages": [], "location": {"line": 170, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Public Routes"], "fullName": "Authentication Guards Integration Tests Public Routes should allow unauthenticated users to access /help", "status": "passed", "title": "should allow unauthenticated users to access /help", "duration": 0.48774800000137475, "failureMessages": [], "location": {"line": 189, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Public Routes"], "fullName": "Authentication Guards Integration Tests Public Routes should allow authenticated users to access /help", "status": "passed", "title": "should allow authenticated users to access /help", "duration": 0.49762700000064797, "failureMessages": [], "location": {"line": 202, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Authentication Initialization"], "fullName": "Authentication Guards Integration Tests Authentication Initialization should initialize auth store if not initialized", "status": "passed", "title": "should initialize auth store if not initialized", "duration": 2.5726679999988846, "failureMessages": [], "location": {"line": 218, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Authentication Initialization"], "fullName": "Authentication Guards Integration Tests Authentication Initialization should handle auth initialization failure gracefully", "status": "passed", "title": "should handle auth initialization failure gracefully", "duration": 4.489439999999377, "failureMessages": [], "location": {"line": 231, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Authentication Initialization"], "fullName": "Authentication Guards Integration Tests Authentication Initialization should not initialize auth store if already initialized", "status": "passed", "title": "should not initialize auth store if already initialized", "duration": 2.348797000000559, "failureMessages": [], "location": {"line": 245, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Redirect Preservation"], "fullName": "Authentication Guards Integration Tests Redirect Preservation should preserve redirect query parameter when redirecting to login", "status": "passed", "title": "should preserve redirect query parameter when redirecting to login", "duration": 0.628936999999496, "failureMessages": [], "location": {"line": 261, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Redirect Preservation"], "fullName": "Authentication Guards Integration Tests Redirect Preservation should handle complex paths with query parameters", "status": "passed", "title": "should handle complex paths with query parameters", "duration": 0.8762079999996786, "failureMessages": [], "location": {"line": 277, "column": 7}, "meta": {}}], "startTime": 1753905136224, "endTime": 1753905136328.8762, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-guards.test.js"}, {"assertionResults": [{"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify admin users", "status": "passed", "title": "should correctly identify admin users", "duration": 5.004893999999695, "failureMessages": [], "location": {"line": 107, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should correctly identify regular users", "status": "passed", "title": "should correctly identify regular users", "duration": 0.7770629999995435, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Checking should handle multiple roles", "status": "passed", "title": "should handle multiple roles", "duration": 0.516751999999542, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should check individual permissions correctly", "status": "passed", "title": "should check individual permissions correctly", "duration": 0.558976000000257, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle admin users with all permissions", "status": "passed", "title": "should handle admin users with all permissions", "duration": 0.4850189999997383, "failureMessages": [], "location": {"line": 167, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Permission Checking should handle users with no permissions", "status": "passed", "title": "should handle users with no permissions", "duration": 0.42240599999968254, "failureMessages": [], "location": {"line": 182, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should allow admin access to admin routes", "status": "passed", "title": "should allow admin access to admin routes", "duration": 9.545121999999537, "failureMessages": [], "location": {"line": 197, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny regular user access to admin routes", "status": "passed", "title": "should deny regular user access to admin routes", "duration": 2.743812999999136, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce role-based route access", "status": "passed", "title": "should enforce role-based route access", "duration": 0.611786999999822, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required role", "status": "passed", "title": "should deny access when user lacks required role", "duration": 0.5705889999990177, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should enforce permission-based route access", "status": "passed", "title": "should enforce permission-based route access", "duration": 0.44416199999977835, "failureMessages": [], "location": {"line": 253, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should deny access when user lacks required permissions", "status": "passed", "title": "should deny access when user lacks required permissions", "duration": 0.48682499999995343, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Route Access Control"], "fullName": "Role-Based Access Control Tests (TEST-007) Route Access Control should redirect unauthenticated users to login", "status": "passed", "title": "should redirect unauthenticated users to login", "duration": 0.3783949999997276, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should handle routes with multiple requirements", "status": "passed", "title": "should handle routes with multiple requirements", "duration": 0.4166640000003099, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Complex Access Control Scenarios"], "fullName": "Role-Based Access Control Tests (TEST-007) Complex Access Control Scenarios should deny access if any requirement is not met", "status": "passed", "title": "should deny access if any requirement is not met", "duration": 0.4761689999995724, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should check resource-specific permissions", "status": "passed", "title": "should check resource-specific permissions", "duration": 0.3445819999997184, "failureMessages": [], "location": {"line": 348, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Dynamic Permission Checking"], "fullName": "Role-Based Access Control Tests (TEST-007) Dynamic Permission Checking should handle hierarchical permissions", "status": "passed", "title": "should handle hierarchical permissions", "duration": 0.34645999999884225, "failureMessages": [], "location": {"line": 363, "column": 7}, "meta": {}}, {"ancestorTitles": ["Role-Based Access Control Tests (TEST-007)", "Role Inheritance"], "fullName": "Role-Based Access Control Tests (TEST-007) Role Inheritance should handle role inheritance correctly", "status": "passed", "title": "should handle role inheritance correctly", "duration": 0.3335349999997561, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1753905138576, "endTime": 1753905138601.3464, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/rbac.test.js"}, {"assertionResults": [{"ancestorTitles": ["useHierarchicalTasks", "Basic Parent-Child Relationships (via parent_id)"], "fullName": "useHierarchicalTasks Basic Parent-Child Relationships (via parent_id) should organize simple parent-child hierarchy", "status": "passed", "title": "should organize simple parent-child hierarchy", "duration": 7.772171000000526, "failureMessages": [], "location": {"line": 18, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Basic Parent-Child Relationships (via parent_id)"], "fullName": "useHierarchicalTasks Basic Parent-Child Relationships (via parent_id) should handle multi-level parent-child hierarchy", "status": "passed", "title": "should handle multi-level parent-child hierarchy", "duration": 1.073178999999982, "failureMessages": [], "location": {"line": 36, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Dependency Relationships (via linked_tasks with Requires)"], "fullName": "useHierarchicalTasks Dependency Relationships (via linked_tasks with Requires) should handle simple dependency chain with object format", "status": "passed", "title": "should handle simple dependency chain with object format", "duration": 6.119768999999906, "failureMessages": [], "location": {"line": 54, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Dependency Relationships (via linked_tasks with Requires)"], "fullName": "useHierarchicalTasks Dependency Relationships (via linked_tasks with Requires) should handle complex dependency chains", "status": "passed", "title": "should handle complex dependency chains", "duration": 0.44516699999985576, "failureMessages": [], "location": {"line": 70, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Parent LinkType (Tight Coupling)"], "fullName": "useHierarchicalTasks Parent LinkType (Tight Coupling) should handle Parent linkType for tight coupling", "status": "passed", "title": "should handle Parent linkType for tight coupling", "duration": 0.38522700000066834, "failureMessages": [], "location": {"line": 90, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Current Data Format (String Arrays)"], "fullName": "useHierarchicalTasks Current Data Format (String Arrays) should handle string-based linked_tasks from current data", "status": "passed", "title": "should handle string-based linked_tasks from current data", "duration": 0.6441150000000562, "failureMessages": [], "location": {"line": 110, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Current Data Format (String Arrays)"], "fullName": "useHierarchicalTasks Current Data Format (String Arrays) should handle real backlog data structure", "status": "passed", "title": "should handle real backlog data structure", "duration": 0.4291560000001482, "failureMessages": [], "location": {"line": 124, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Mixed Relationships"], "fullName": "useHierarchicalTasks Mixed Relationships should handle tasks with both parent_id and linked_tasks", "status": "passed", "title": "should handle tasks with both parent_id and linked_tasks", "duration": 0.4251930000000357, "failureMessages": [], "location": {"line": 152, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Edge Cases"], "fullName": "useHierarchicalTasks Edge Cases should handle empty task list", "status": "passed", "title": "should handle empty task list", "duration": 0.23605700000007346, "failureMessages": [], "location": {"line": 172, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Edge Cases"], "fullName": "useHierarchicalTasks Edge Cases should handle null/undefined input", "status": "passed", "title": "should handle null/undefined input", "duration": 0.3167329999996582, "failureMessages": [], "location": {"line": 177, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Edge Cases"], "fullName": "useHierarchicalTasks Edge Cases should handle tasks with missing task_id", "status": "passed", "title": "should handle tasks with missing task_id", "duration": 0.24685700000009092, "failureMessages": [], "location": {"line": 182, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Edge Cases"], "fullName": "useHierarchicalTasks Edge Cases should detect and handle circular dependencies", "status": "passed", "title": "should detect and handle circular dependencies", "duration": 2.046698999999535, "failureMessages": [], "location": {"line": 193, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Edge Cases"], "fullName": "useHierarchicalTasks Edge Cases should handle orphaned tasks (references to non-existent tasks)", "status": "passed", "title": "should handle orphaned tasks (references to non-existent tasks)", "duration": 0.40138200000001234, "failureMessages": [], "location": {"line": 208, "column": 7}, "meta": {}}, {"ancestorTitles": ["useHierarchicalTasks", "Performance Tests"], "fullName": "useHierarchicalTasks Performance Tests should handle large datasets efficiently", "status": "passed", "title": "should handle large datasets efficiently", "duration": 19.532947000000604, "failureMessages": [], "location": {"line": 221, "column": 7}, "meta": {}}], "startTime": 1753905135337, "endTime": 1753905135378.533, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/tests/useHierarchicalTasks.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Composable (TEST-002)"], "fullName": "Authentication Composable (TEST-002) should be properly configured", "status": "passed", "title": "should be properly configured", "duration": 3.399230000000898, "failureMessages": [], "location": {"line": 149, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should provide access to authentication state", "status": "passed", "title": "should provide access to authentication state", "duration": 0.7347840000002179, "failureMessages": [], "location": {"line": 154, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "State Access"], "fullName": "Authentication Composable (TEST-002) State Access should reflect changes in authentication state", "status": "passed", "title": "should reflect changes in authentication state", "duration": 1.5047709999998915, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should login and redirect on success", "status": "passed", "title": "should login and redirect on success", "duration": 2.1308199999984936, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Login Function"], "fullName": "Authentication Composable (TEST-002) Login Function should not redirect on login failure", "status": "passed", "title": "should not redirect on login failure", "duration": 0.8088490000009187, "failureMessages": [], "location": {"line": 191, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should register and redirect to login on success", "status": "passed", "title": "should register and redirect to login on success", "duration": 5.871758000001137, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Register Function"], "fullName": "Authentication Composable (TEST-002) Register Function should not redirect on registration failure", "status": "passed", "title": "should not redirect on registration failure", "duration": 0.4519159999999829, "failureMessages": [], "location": {"line": 219, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Logout Function"], "fullName": "Authentication Composable (TEST-002) Logout Function should logout and redirect to login", "status": "passed", "title": "should logout and redirect to login", "duration": 0.42434900000080233, "failureMessages": [], "location": {"line": 233, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Token Refresh Function"], "fullName": "Authentication Composable (TEST-002) Token Refresh Function should refresh token", "status": "passed", "title": "should refresh token", "duration": 0.41623099999924307, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should request password reset", "status": "passed", "title": "should request password reset", "duration": 0.43494099999952596, "failureMessages": [], "location": {"line": 259, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Password Reset Functions"], "fullName": "Authentication Composable (TEST-002) Password Reset Functions should reset password and redirect to login on success", "status": "passed", "title": "should reset password and redirect to login on success", "duration": 0.523403999999573, "failureMessages": [], "location": {"line": 271, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should update profile", "status": "passed", "title": "should update profile", "duration": 0.42839699999967706, "failureMessages": [], "location": {"line": 286, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Profile Management Functions"], "fullName": "Authentication Composable (TEST-002) Profile Management Functions should change password", "status": "passed", "title": "should change password", "duration": 0.4353590000009717, "failureMessages": [], "location": {"line": 298, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when authenticated (requireAuth)", "status": "passed", "title": "should allow access when authenticated (requireAuth)", "duration": 0.2914089999994758, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to login when not authenticated (requireAuth)", "status": "passed", "title": "should redirect to login when not authenticated (requireAuth)", "duration": 0.3011350000015227, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should allow access when not authenticated (requireGuest)", "status": "passed", "title": "should allow access when not authenticated (requireGuest)", "duration": 0.29896200000075623, "failureMessages": [], "location": {"line": 332, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Composable (TEST-002)", "Route Guards"], "fullName": "Authentication Composable (TEST-002) Route Guards should redirect to home when authenticated (requireGuest)", "status": "passed", "title": "should redirect to home when authenticated (requireGuest)", "duration": 0.3103630000005069, "failureMessages": [], "location": {"line": 342, "column": 7}, "meta": {}}], "startTime": 1753905138135, "endTime": 1753905138155.3103, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useAuth.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionManagement", "initialization"], "fullName": "useSessionManagement initialization should initialize with empty sessions", "status": "passed", "title": "should initialize with empty sessions", "duration": 17.52143199999955, "failureMessages": [], "location": {"line": 63, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should fetch sessions successfully", "status": "passed", "title": "should fetch sessions successfully", "duration": 6.1900320000004285, "failureMessages": [], "location": {"line": 81, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle fetch errors", "status": "passed", "title": "should handle fetch errors", "duration": 1.1775689999994938, "failureMessages": [], "location": {"line": 142, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API errors", "status": "passed", "title": "should handle API errors", "duration": 0.807730000000447, "failureMessages": [], "location": {"line": 163, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should handle API response errors", "status": "passed", "title": "should handle API response errors", "duration": 0.7828800000006595, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "fetchSessions"], "fullName": "useSessionManagement fetchSessions should not fetch when unauthenticated", "status": "passed", "title": "should not fetch when unauthenticated", "duration": 1.6665460000003804, "failureMessages": [], "location": {"line": 211, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should terminate session successfully", "status": "passed", "title": "should terminate session successfully", "duration": 10.37602700000025, "failureMessages": [], "location": {"line": 225, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 7.682749000001422, "failureMessages": [], "location": {"line": 289, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateSession"], "fullName": "useSessionManagement terminateSession should handle API termination errors", "status": "passed", "title": "should handle API termination errors", "duration": 2.37914500000079, "failureMessages": [], "location": {"line": 306, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should terminate all other sessions successfully", "status": "passed", "title": "should terminate all other sessions successfully", "duration": 1.4336740000017016, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "terminateAllOtherSessions"], "fullName": "useSessionManagement terminateAllOtherSessions should handle termination errors", "status": "passed", "title": "should handle termination errors", "duration": 0.9621719999995548, "failureMessages": [], "location": {"line": 381, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should refresh current session successfully", "status": "passed", "title": "should refresh current session successfully", "duration": 1.2782829999996466, "failureMessages": [], "location": {"line": 400, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "refreshCurrentSession"], "fullName": "useSessionManagement refreshCurrentSession should handle refresh errors", "status": "passed", "title": "should handle refresh errors", "duration": 7.37373000000116, "failureMessages": [], "location": {"line": 431, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute active sessions correctly", "status": "passed", "title": "should compute active sessions correctly", "duration": 8.120799000000261, "failureMessages": [], "location": {"line": 450, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should identify current session correctly", "status": "passed", "title": "should identify current session correctly", "duration": 0.989238000000114, "failureMessages": [], "location": {"line": 482, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute other sessions correctly", "status": "passed", "title": "should compute other sessions correctly", "duration": 11.041638000000603, "failureMessages": [], "location": {"line": 515, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "computed properties"], "fullName": "useSessionManagement computed properties should compute session statistics correctly", "status": "passed", "title": "should compute session statistics correctly", "duration": 3.2274880000004487, "failureMessages": [], "location": {"line": 548, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect device type correctly", "status": "passed", "title": "should detect device type correctly", "duration": 1.1576750000003813, "failureMessages": [], "location": {"line": 587, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "utility functions"], "fullName": "useSessionManagement utility functions should detect browser name correctly", "status": "passed", "title": "should detect browser name correctly", "duration": 0.7969759999996313, "failureMessages": [], "location": {"line": 604, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should get CSRF token successfully", "status": "passed", "title": "should get CSRF token successfully", "duration": 0.9504960000012943, "failureMessages": [], "location": {"line": 623, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "CSRF token handling"], "fullName": "useSessionManagement CSRF token handling should handle CSRF token errors gracefully", "status": "passed", "title": "should handle CSRF token errors gracefully", "duration": 1.5558729999993375, "failureMessages": [], "location": {"line": 652, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should start auto refresh", "status": "passed", "title": "should start auto refresh", "duration": 2.844779999999446, "failureMessages": [], "location": {"line": 673, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should stop auto refresh", "status": "passed", "title": "should stop auto refresh", "duration": 0.9344020000007731, "failureMessages": [], "location": {"line": 682, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "auto refresh"], "fullName": "useSessionManagement auto refresh should refresh sessions automatically", "status": "passed", "title": "should refresh sessions automatically", "duration": 1.2070679999997083, "failureMessages": [], "location": {"line": 693, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "error handling"], "fullName": "useSessionManagement error handling should clear errors", "status": "passed", "title": "should clear errors", "duration": 0.8461139999999432, "failureMessages": [], "location": {"line": 714, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should detect new sessions", "status": "passed", "title": "should detect new sessions", "duration": 1.6207699999995384, "failureMessages": [], "location": {"line": 737, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionManagement", "new session detection"], "fullName": "useSessionManagement new session detection should handle no new sessions", "status": "passed", "title": "should handle no new sessions", "duration": 1.2518570000011096, "failureMessages": [], "location": {"line": 789, "column": 7}, "meta": {}}], "startTime": 1753905135770, "endTime": 1753905135867.252, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionManagement.test.js"}, {"assertionResults": [{"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should initialize with default configuration", "status": "passed", "title": "should initialize with default configuration", "duration": 8.003373999999894, "failureMessages": [], "location": {"line": 97, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "initialization"], "fullName": "useSessionTimeout initialization should accept custom configuration", "status": "passed", "title": "should accept custom configuration", "duration": 1.5039059999999154, "failureMessages": [], "location": {"line": 113, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should register activity event listeners", "status": "passed", "title": "should register activity event listeners", "duration": 2.553411999999298, "failureMessages": [], "location": {"line": 128, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should update activity timestamp on user activity", "status": "passed", "title": "should update activity timestamp on user activity", "duration": 4.352289999999812, "failureMessages": [], "location": {"line": 141, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "activity tracking"], "fullName": "useSessionTimeout activity tracking should handle localStorage errors gracefully", "status": "passed", "title": "should handle localStorage errors gracefully", "duration": 4.291251999999986, "failureMessages": [], "location": {"line": 158, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should show warning when approaching timeout", "status": "passed", "title": "should show warning when approaching timeout", "duration": 3.865060999999514, "failureMessages": [], "location": {"line": 170, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should hide warning when activity is detected", "status": "passed", "title": "should hide warning when activity is detected", "duration": 26.02476200000001, "failureMessages": [], "location": {"line": 186, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "warning system"], "fullName": "useSessionTimeout warning system should format time remaining correctly", "status": "passed", "title": "should format time remaining correctly", "duration": 1.4365560000005644, "failureMessages": [], "location": {"line": 206, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should extend session successfully", "status": "passed", "title": "should extend session successfully", "duration": 3.9404459999996106, "failureMessages": [], "location": {"line": 226, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle session extension failure", "status": "passed", "title": "should handle session extension failure", "duration": 1.334683000000041, "failureMessages": [], "location": {"line": 246, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session extension"], "fullName": "useSessionTimeout session extension should handle network errors during extension", "status": "passed", "title": "should handle network errors during extension", "duration": 1.1183899999996356, "failureMessages": [], "location": {"line": 261, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should logout user when session times out", "status": "passed", "title": "should logout user when session times out", "duration": 0.8695720000005167, "failureMessages": [], "location": {"line": 274, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session timeout handling"], "fullName": "useSessionTimeout session timeout handling should redirect to login even if logout fails", "status": "passed", "title": "should redirect to login even if logout fails", "duration": 0.7885029999997641, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should check for activity in other tabs", "status": "passed", "title": "should check for activity in other tabs", "duration": 1.3274059999994279, "failureMessages": [], "location": {"line": 296, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cross-tab communication"], "fullName": "useSessionTimeout cross-tab communication should handle localStorage errors during cross-tab check", "status": "passed", "title": "should handle localStorage errors during cross-tab check", "duration": 1.1190800000003946, "failureMessages": [], "location": {"line": 310, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should remove event listeners when stopped", "status": "passed", "title": "should remove event listeners when stopped", "duration": 1.2680849999997008, "failureMessages": [], "location": {"line": 322, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "cleanup"], "fullName": "useSessionTimeout cleanup should clear timers when stopped", "status": "passed", "title": "should clear timers when stopped", "duration": 40.35101100000065, "failureMessages": [], "location": {"line": 333, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "session status"], "fullName": "useSessionTimeout session status should provide comprehensive session status", "status": "passed", "title": "should provide comprehensive session status", "duration": 14.724793000000318, "failureMessages": [], "location": {"line": 345, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle unauthenticated state gracefully", "status": "passed", "title": "should handle unauthenticated state gracefully", "duration": 2.062267000000247, "failureMessages": [], "location": {"line": 370, "column": 7}, "meta": {}}, {"ancestorTitles": ["useSessionTimeout", "edge cases"], "fullName": "useSessionTimeout edge cases should handle multiple start/stop cycles", "status": "passed", "title": "should handle multiple start/stop cycles", "duration": 3.105880999999499, "failureMessages": [], "location": {"line": 382, "column": 7}, "meta": {}}], "startTime": 1753905131987, "endTime": 1753905132112.106, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/composables/useSessionTimeout.test.js"}, {"assertionResults": [{"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should generate and set CSRF token in cookie and header", "status": "passed", "title": "should generate and set CSRF token in cookie and header", "duration": 114.25669099999959, "failureMessages": [], "location": {"line": 38, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "generateCSRFToken"], "fullName": "CSRF Middleware generateCSRFToken should set secure cookie in production", "status": "passed", "title": "should set secure cookie in production", "duration": 12.691219999999703, "failureMessages": [], "location": {"line": 53, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for GET requests", "status": "passed", "title": "should skip validation for GET requests", "duration": 4.794625000000451, "failureMessages": [], "location": {"line": 70, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for HEAD requests", "status": "passed", "title": "should skip validation for HEAD requests", "duration": 4.814892000000327, "failureMessages": [], "location": {"line": 79, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "validateCSRFToken"], "fullName": "CSRF Middleware validateCSRFToken should skip validation for OPTIONS requests", "status": "passed", "title": "should skip validation for OPTIONS requests", "duration": 30.37563499999942, "failureMessages": [], "location": {"line": 88, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect authentication endpoints", "status": "passed", "title": "should protect authentication endpoints", "duration": 23.49534400000084, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should protect registration endpoints", "status": "passed", "title": "should protect registration endpoints", "duration": 9.158123999999589, "failureMessages": [], "location": {"line": 114, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect non-authentication endpoints", "status": "passed", "title": "should not protect non-authentication endpoints", "duration": 5.132237000000714, "failureMessages": [], "location": {"line": 123, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "csrfProtection"], "fullName": "CSRF Middleware csrfProtection should not protect GET requests to auth endpoints", "status": "passed", "title": "should not protect GET requests to auth endpoints", "duration": 31.683976000000257, "failureMessages": [], "location": {"line": 132, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "getCSRFToken"], "fullName": "CSRF Middleware getCSRFToken should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.6950889999998253, "failureMessages": [], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "cleanupExpiredTokens"], "fullName": "CSRF Middleware cleanupExpiredTokens should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 2.3422410000002856, "failureMessages": [], "location": {"line": 154, "column": 5}, "meta": {}}, {"ancestorTitles": ["CSRF Middleware", "CSRF token endpoint"], "fullName": "CSRF Middleware CSRF token endpoint should provide CSRF token via API endpoint", "status": "passed", "title": "should provide CSRF token via API endpoint", "duration": 4.648159000000305, "failureMessages": [], "location": {"line": 162, "column": 5}, "meta": {}}], "startTime": 1753905132505, "endTime": 1753905132749.6482, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/csrf.test.js"}, {"assertionResults": [{"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should store reset token with expiration", "status": "passed", "title": "should store reset token with expiration", "duration": 5.468286999999691, "failureMessages": [], "location": {"line": 39, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "storeResetToken"], "fullName": "Password Reset Middleware storeResetToken should use default expiration time", "status": "passed", "title": "should use default expiration time", "duration": 1.3583090000001903, "failureMessages": [], "location": {"line": 55, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should validate valid token", "status": "passed", "title": "should validate valid token", "duration": 0.9287950000007186, "failureMessages": [], "location": {"line": 66, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject non-existent token", "status": "passed", "title": "should reject non-existent token", "duration": 2.825587999999698, "failureMessages": [], "location": {"line": 78, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject expired token", "status": "passed", "title": "should reject expired token", "duration": 0.5777699999998731, "failureMessages": [], "location": {"line": 85, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject used token", "status": "passed", "title": "should reject used token", "duration": 0.6343759999999747, "failureMessages": [], "location": {"line": 100, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should increment attempt counter", "status": "passed", "title": "should increment attempt counter", "duration": 0.5138209999995524, "failureMessages": [], "location": {"line": 113, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetToken"], "fullName": "Password Reset Middleware validateResetToken should reject token after too many attempts", "status": "passed", "title": "should reject token after too many attempts", "duration": 0.6839329999993424, "failureMessages": [], "location": {"line": 126, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should mark token as used", "status": "passed", "title": "should mark token as used", "duration": 0.6204490000000078, "failureMessages": [], "location": {"line": 146, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "markTokenAsUsed"], "fullName": "Password Reset Middleware markTokenAsUsed should handle non-existent token gracefully", "status": "passed", "title": "should handle non-existent token gracefully", "duration": 10.280759000000216, "failureMessages": [], "location": {"line": 157, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "invalidateEmailResetTokens"], "fullName": "Password Reset Middleware invalidateEmailResetTokens should invalidate existing tokens for email", "status": "passed", "title": "should invalidate existing tokens for email", "duration": 0.626006999999845, "failureMessages": [], "location": {"line": 163, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should allow requests within rate limit", "status": "passed", "title": "should allow requests within rate limit", "duration": 107.68890800000008, "failureMessages": [], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "passwordResetRateLimit"], "fullName": "Password Reset Middleware passwordResetRateLimit should block requests exceeding rate limit", "status": "passed", "title": "should block requests exceeding rate limit", "duration": 40.572941999999784, "failureMessages": [], "location": {"line": 198, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should allow requests for different emails", "status": "passed", "title": "should allow requests for different emails", "duration": 6.876703999999336, "failureMessages": [], "location": {"line": 228, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "emailResetRateLimit"], "fullName": "Password Reset Middleware emailResetRateLimit should block excessive requests for same email", "status": "passed", "title": "should block excessive requests for same email", "duration": 29.077212999999574, "failureMessages": [], "location": {"line": 246, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should validate token and attach data to request", "status": "passed", "title": "should validate token and attach data to request", "duration": 80.89814000000024, "failureMessages": [], "location": {"line": 277, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request with invalid token", "status": "passed", "title": "should reject request with invalid token", "duration": 18.232508000000053, "failureMessages": [], "location": {"line": 301, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "validateResetTokenMiddleware"], "fullName": "Password Reset Middleware validateResetTokenMiddleware should reject request without token", "status": "passed", "title": "should reject request without token", "duration": 61.55813000000035, "failureMessages": [], "location": {"line": 313, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should not delay first request", "status": "passed", "title": "should not delay first request", "duration": 30.938952999999856, "failureMessages": [], "location": {"line": 327, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "progressiveDelayMiddleware"], "fullName": "Password Reset Middleware progressiveDelayMiddleware should handle missing email gracefully", "status": "passed", "title": "should handle missing email gracefully", "duration": 3.8501649999998335, "failureMessages": [], "location": {"line": 342, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should clean up expired tokens", "status": "passed", "title": "should clean up expired tokens", "duration": 0.46586500000012165, "failureMessages": [], "location": {"line": 356, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "cleanupExpiredData"], "fullName": "Password Reset Middleware cleanupExpiredData should not throw errors during cleanup", "status": "passed", "title": "should not throw errors during cleanup", "duration": 1.8092440000000352, "failureMessages": [], "location": {"line": 373, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "getResetTokenStats"], "fullName": "Password Reset Middleware getResetTokenStats should return correct statistics", "status": "passed", "title": "should return correct statistics", "duration": 1.2860300000002098, "failureMessages": [], "location": {"line": 379, "column": 5}, "meta": {}}, {"ancestorTitles": ["Password Reset Middleware", "Integration tests"], "fullName": "Password Reset Middleware Integration tests should handle complete password reset flow", "status": "passed", "title": "should handle complete password reset flow", "duration": 0.5149789999995846, "failureMessages": [], "location": {"line": 404, "column": 5}, "meta": {}}], "startTime": 1753905132583, "endTime": 1753905132992.515, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/middleware/passwordReset.test.js"}, {"assertionResults": [{"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should initialize auth store if not initialized", "status": "passed", "title": "should initialize auth store if not initialized", "duration": 3.560712999998941, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Authentication Guard"], "fullName": "Route Guards (TEST-003) Authentication Guard should not initialize auth store if already initialized", "status": "passed", "title": "should not initialize auth store if already initialized", "duration": 0.7778490000000602, "failureMessages": [], "location": {"line": 120, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should allow access to protected routes when authenticated", "status": "passed", "title": "should allow access to protected routes when authenticated", "duration": 0.968047000000297, "failureMessages": [], "location": {"line": 135, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should redirect to login when accessing protected routes while unauthenticated", "status": "passed", "title": "should redirect to login when accessing protected routes while unauthenticated", "duration": 2.2331349999985832, "failureMessages": [], "location": {"line": 148, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Protected Routes"], "fullName": "Route Guards (TEST-003) Protected Routes should preserve query parameters in redirect", "status": "passed", "title": "should preserve query parameters in redirect", "duration": 16.77529499999946, "failureMessages": [], "location": {"line": 164, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should allow access to guest-only routes when unauthenticated", "status": "passed", "title": "should allow access to guest-only routes when unauthenticated", "duration": 0.9281709999995655, "failureMessages": [], "location": {"line": 183, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect to home when accessing guest-only routes while authenticated", "status": "passed", "title": "should redirect to home when accessing guest-only routes while authenticated", "duration": 2.2119020000009186, "failureMessages": [], "location": {"line": 196, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from register page", "status": "passed", "title": "should redirect authenticated users from register page", "duration": 1.0355409999992844, "failureMessages": [], "location": {"line": 209, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Guest-Only Routes"], "fullName": "Route Guards (TEST-003) Guest-Only Routes should redirect authenticated users from forgot-password page", "status": "passed", "title": "should redirect authenticated users from forgot-password page", "duration": 0.843990000001213, "failureMessages": [], "location": {"line": 222, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should allow access to admin routes when user is admin", "status": "passed", "title": "should allow access to admin routes when user is admin", "duration": 0.5312599999997474, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to unauthorized when non-admin user accesses admin routes", "status": "passed", "title": "should redirect to unauthorized when non-admin user accesses admin routes", "duration": 0.5264220000008208, "failureMessages": [], "location": {"line": 251, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Admin Routes"], "fullName": "Route Guards (TEST-003) Admin Routes should redirect to login when unauthenticated user accesses admin routes", "status": "passed", "title": "should redirect to login when unauthenticated user accesses admin routes", "duration": 0.5164320000003499, "failureMessages": [], "location": {"line": 265, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow access to public routes regardless of authentication status", "status": "passed", "title": "should allow access to public routes regardless of authentication status", "duration": 0.408966999999393, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Public Routes"], "fullName": "Route Guards (TEST-003) Public Routes should allow authenticated users to access public routes", "status": "passed", "title": "should allow authenticated users to access public routes", "duration": 0.35228900000038266, "failureMessages": [], "location": {"line": 297, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Erro<PERSON>"], "fullName": "Route Guards (TEST-003) Error Handling should handle auth initialization errors gracefully", "status": "passed", "title": "should handle auth initialization errors gracefully", "duration": 0.6994860000013432, "failureMessages": [], "location": {"line": 312, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should handle routes with multiple meta requirements", "status": "passed", "title": "should handle routes with multiple meta requirements", "duration": 0.781329999999798, "failureMessages": [], "location": {"line": 329, "column": 7}, "meta": {}}, {"ancestorTitles": ["Route Guards (TEST-003)", "Route Meta Combinations"], "fullName": "Route Guards (TEST-003) Route Meta Combinations should prioritize authentication check over admin check", "status": "passed", "title": "should prioritize authentication check over admin check", "duration": 0.5230609999998705, "failureMessages": [], "location": {"line": 346, "column": 7}, "meta": {}}], "startTime": 1753905138130, "endTime": 1753905138165.523, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/router/guards.test.js"}, {"assertionResults": [{"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should login user with valid credentials", "status": "passed", "title": "should login user with valid credentials", "duration": 9.55251600000156, "failureMessages": [], "location": {"line": 90, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should throw error for invalid credentials", "status": "passed", "title": "should throw error for invalid credentials", "duration": 2.5466489999998885, "failureMessages": [], "location": {"line": 115, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "login"], "fullName": "AuthService login should throw error when no record returned", "status": "passed", "title": "should throw error when no record returned", "duration": 0.6814240000003338, "failureMessages": [], "location": {"line": 122, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should register user with valid data", "status": "passed", "title": "should register user with valid data", "duration": 2.1463589999984833, "failureMessages": [], "location": {"line": 131, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should handle registration validation errors", "status": "passed", "title": "should handle registration validation errors", "duration": 0.6438220000000001, "failureMessages": [], "location": {"line": 161, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "register"], "fullName": "AuthService register should continue registration even if verification email fails", "status": "passed", "title": "should continue registration even if verification email fails", "duration": 0.5979680000000371, "failureMessages": [], "location": {"line": 175, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "logout"], "fullName": "AuthService logout should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 0.5453560000005382, "failureMessages": [], "location": {"line": 199, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "refreshToken"], "fullName": "AuthService refreshToken should refresh token when valid", "status": "passed", "title": "should refresh token when valid", "duration": 0.47423299999900337, "failureMessages": [], "location": {"line": 208, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "refreshToken"], "fullName": "AuthService refreshToken should throw error for invalid token", "status": "passed", "title": "should throw error for invalid token", "duration": 0.7451299999993353, "failureMessages": [], "location": {"line": 218, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "requestPasswordReset"], "fullName": "AuthService requestPasswordReset should request password reset", "status": "passed", "title": "should request password reset", "duration": 1.2287070000002132, "failureMessages": [], "location": {"line": 227, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "requestPasswordReset"], "fullName": "AuthService requestPasswordReset should return success even if email does not exist", "status": "passed", "title": "should return success even if email does not exist", "duration": 0.7127909999999247, "failureMessages": [], "location": {"line": 237, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "resetPassword"], "fullName": "AuthService resetPassword should reset password with valid token", "status": "passed", "title": "should reset password with valid token", "duration": 0.8448339999995369, "failureMessages": [], "location": {"line": 248, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "resetPassword"], "fullName": "AuthService resetPassword should throw error for invalid token", "status": "passed", "title": "should throw error for invalid token", "duration": 0.4522189999988768, "failureMessages": [], "location": {"line": 260, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "getCurrentUser"], "fullName": "AuthService getCurrentUser should get current user profile", "status": "passed", "title": "should get current user profile", "duration": 0.5702359999995679, "failureMessages": [], "location": {"line": 272, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "getCurrentUser"], "fullName": "AuthService getCurrentUser should throw error for non-existent user", "status": "passed", "title": "should throw error for non-existent user", "duration": 0.41216500000155065, "failureMessages": [], "location": {"line": 293, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "updateProfile"], "fullName": "AuthService updateProfile should update user profile", "status": "passed", "title": "should update user profile", "duration": 0.8250570000000153, "failureMessages": [], "location": {"line": 302, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "updateProfile"], "fullName": "AuthService updateProfile should handle validation errors", "status": "passed", "title": "should handle validation errors", "duration": 0.9612250000009226, "failureMessages": [], "location": {"line": 327, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "changePassword"], "fullName": "AuthService changePassword should change password successfully", "status": "passed", "title": "should change password successfully", "duration": 0.9066849999999249, "failureMessages": [], "location": {"line": 340, "column": 7}, "meta": {}}, {"ancestorTitles": ["AuthService", "changePassword"], "fullName": "AuthService changePassword should throw error for incorrect current password", "status": "passed", "title": "should throw error for incorrect current password", "duration": 0.5650320000004285, "failureMessages": [], "location": {"line": 354, "column": 7}, "meta": {}}], "startTime": 1753905138916, "endTime": 1753905138942.565, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/authService.test.js"}, {"assertionResults": [{"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should create a new session with valid data", "status": "passed", "title": "should create a new session with valid data", "duration": 4.574252000000342, "failureMessages": [], "location": {"line": 23, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "createSession"], "fullName": "SessionService createSession should generate unique session IDs", "status": "passed", "title": "should generate unique session IDs", "duration": 1.4530350000004546, "failureMessages": [], "location": {"line": 42, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should retrieve session by ID", "status": "passed", "title": "should retrieve session by ID", "duration": 0.4781039999998029, "failureMessages": [], "location": {"line": 51, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSession"], "fullName": "SessionService getSession should return null for non-existent session", "status": "passed", "title": "should return null for non-existent session", "duration": 0.32992299999932584, "failureMessages": [], "location": {"line": 60, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should retrieve session by token", "status": "passed", "title": "should retrieve session by token", "duration": 0.4607319999995525, "failureMessages": [], "location": {"line": 67, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionByToken"], "fullName": "SessionService getSessionByToken should return null for non-existent token", "status": "passed", "title": "should return null for non-existent token", "duration": 0.419170999999551, "failureMessages": [], "location": {"line": 77, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return all active sessions for a user", "status": "passed", "title": "should return all active sessions for a user", "duration": 16.926755000000412, "failureMessages": [], "location": {"line": 84, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessions"], "fullName": "SessionService getUserSessions should return empty array for user with no sessions", "status": "passed", "title": "should return empty array for user with no sessions", "duration": 0.3793709999990824, "failureMessages": [], "location": {"line": 99, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should update last activity for valid session", "status": "passed", "title": "should update last activity for valid session", "duration": 15.216122000001633, "failureMessages": [], "location": {"line": 106, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 2.5047599999998056, "failureMessages": [], "location": {"line": 121, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "updateSessionActivity"], "fullName": "SessionService updateSessionActivity should return false for inactive session", "status": "passed", "title": "should return false for inactive session", "duration": 6.205187999999907, "failureMessages": [], "location": {"line": 126, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should invalidate session by ID", "status": "passed", "title": "should invalidate session by ID", "duration": 1.669822000001659, "failureMessages": [], "location": {"line": 139, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should add token to blacklist when invalidating", "status": "passed", "title": "should add token to blacklist when invalidating", "duration": 0.7953030000007857, "failureMessages": [], "location": {"line": 149, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSession"], "fullName": "SessionService invalidateSession should return false for non-existent session", "status": "passed", "title": "should return false for non-existent session", "duration": 0.27568000000064785, "failureMessages": [], "location": {"line": 159, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should invalidate session by token", "status": "passed", "title": "should invalidate session by token", "duration": 0.38709600000038336, "failureMessages": [], "location": {"line": 166, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateSessionByToken"], "fullName": "SessionService invalidateSessionByToken should return false for non-existent token", "status": "passed", "title": "should return false for non-existent token", "duration": 0.25154900000052294, "failureMessages": [], "location": {"line": 178, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should invalidate all sessions for a user", "status": "passed", "title": "should invalidate all sessions for a user", "duration": 0.4960460000002058, "failureMessages": [], "location": {"line": 185, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "invalidateAllUserSessions"], "fullName": "SessionService invalidateAllUserSessions should exclude specified token from invalidation", "status": "passed", "title": "should exclude specified token from invalidation", "duration": 0.37779200000113633, "failureMessages": [], "location": {"line": 203, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return true for blacklisted tokens", "status": "passed", "title": "should return true for blacklisted tokens", "duration": 0.22720200000003388, "failureMessages": [], "location": {"line": 221, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "isTokenBlacklisted"], "fullName": "SessionService isTokenBlacklisted should return false for non-blacklisted tokens", "status": "passed", "title": "should return false for non-blacklisted tokens", "duration": 0.3299360000000888, "failureMessages": [], "location": {"line": 230, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should validate active session and update activity", "status": "passed", "title": "should validate active session and update activity", "duration": 0.3457909999997355, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject blacklisted tokens", "status": "passed", "title": "should reject blacklisted tokens", "duration": 0.2269079999987298, "failureMessages": [], "location": {"line": 250, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject non-existent sessions", "status": "passed", "title": "should reject non-existent sessions", "duration": 0.1878530000012688, "failureMessages": [], "location": {"line": 262, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "validateSession"], "fullName": "SessionService validateSession should reject inactive sessions", "status": "passed", "title": "should reject inactive sessions", "duration": 0.2256149999993795, "failureMessages": [], "location": {"line": 269, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getSessionStats"], "fullName": "SessionService getSessionStats should return correct session statistics", "status": "passed", "title": "should return correct session statistics", "duration": 0.4123029999991559, "failureMessages": [], "location": {"line": 284, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "forceLogoutUser"], "fullName": "SessionService forceLogoutUser should force logout all sessions for a user", "status": "passed", "title": "should force logout all sessions for a user", "duration": 0.43737099999998463, "failureMessages": [], "location": {"line": 304, "column": 7}, "meta": {}}, {"ancestorTitles": ["SessionService", "getUserSessionDetails"], "fullName": "SessionService getUserSessionDetails should return detailed session information", "status": "passed", "title": "should return detailed session information", "duration": 1.2689559999998892, "failureMessages": [], "location": {"line": 319, "column": 7}, "meta": {}}], "startTime": 1753905136311, "endTime": 1753905136369.269, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/services/sessionService.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should login user with valid credentials", "status": "passed", "title": "should login user with valid credentials", "duration": 135.20001500000035, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 16.270486000000346, "failureMessages": [], "location": {"line": 232, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/login"], "fullName": "Authentication Routes POST /api/auth/login should return validation error for missing password", "status": "passed", "title": "should return validation error for missing password", "duration": 7.231244999999944, "failureMessages": [], "location": {"line": 245, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should register user with valid data", "status": "passed", "title": "should register user with valid data", "duration": 7.67288599999938, "failureMessages": [], "location": {"line": 259, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for weak password", "status": "passed", "title": "should return validation error for weak password", "duration": 4.185654999999315, "failureMessages": [], "location": {"line": 293, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/register"], "fullName": "Authentication Routes POST /api/auth/register should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 3.463704000000689, "failureMessages": [], "location": {"line": 308, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/logout"], "fullName": "Authentication Routes POST /api/auth/logout should logout user successfully", "status": "passed", "title": "should logout user successfully", "duration": 16.329732999999578, "failureMessages": [], "location": {"line": 325, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 15.41506599999957, "failureMessages": [], "location": {"line": 343, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/refresh"], "fullName": "Authentication Routes POST /api/auth/refresh should return validation error for missing refresh token", "status": "passed", "title": "should return validation error for missing refresh token", "duration": 15.927405000000363, "failureMessages": [], "location": {"line": 363, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should send password reset email", "status": "passed", "title": "should send password reset email", "duration": 53.420310999999856, "failureMessages": [], "location": {"line": 375, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/forgot-password"], "fullName": "Authentication Routes POST /api/auth/forgot-password should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 14.448777999999947, "failureMessages": [], "location": {"line": 393, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 3.3494799999998577, "failureMessages": [], "location": {"line": 407, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "POST /api/auth/reset-password"], "fullName": "Authentication Routes POST /api/auth/reset-password should return validation error for missing token", "status": "passed", "title": "should return validation error for missing token", "duration": 8.525618000000577, "failureMessages": [], "location": {"line": 427, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "GET /api/auth/me"], "fullName": "Authentication Routes GET /api/auth/me should get current user profile", "status": "passed", "title": "should get current user profile", "duration": 52.0360869999995, "failureMessages": [], "location": {"line": 442, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should update user profile", "status": "passed", "title": "should update user profile", "duration": 40.34662599999956, "failureMessages": [], "location": {"line": 467, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/profile"], "fullName": "Authentication Routes PUT /api/auth/profile should return validation error for invalid email", "status": "passed", "title": "should return validation error for invalid email", "duration": 11.614120999999614, "failureMessages": [], "location": {"line": 499, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should change password successfully", "status": "passed", "title": "should change password successfully", "duration": 30.440068000000792, "failureMessages": [], "location": {"line": 514, "column": 5}, "meta": {}}, {"ancestorTitles": ["Authentication Routes", "PUT /api/auth/password"], "fullName": "Authentication Routes PUT /api/auth/password should return validation error for password mismatch", "status": "passed", "title": "should return validation error for password mismatch", "duration": 22.275017999999363, "failureMessages": [], "location": {"line": 541, "column": 5}, "meta": {}}], "startTime": 1753905132576, "endTime": 1753905133035.275, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/unit/routes/auth.test.js"}, {"assertionResults": [{"ancestorTitles": ["Authentication Store (TEST-001)", "Initial State"], "fullName": "Authentication Store (TEST-001) Initial State should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 6.371981999999662, "failureMessages": [], "location": {"line": 49, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return correct isLoggedIn status", "status": "passed", "title": "should return correct isLoggedIn status", "duration": 2.0674890000000232, "failureMessages": [], "location": {"line": 61, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return current user", "status": "passed", "title": "should return current user", "duration": 1.6916089999995165, "failureMessages": [], "location": {"line": 70, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should login successfully", "status": "passed", "title": "should login successfully", "duration": 3.6785850000005667, "failureMessages": [], "location": {"line": 79, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login failure", "status": "passed", "title": "should handle login failure", "duration": 1.3989040000005843, "failureMessages": [], "location": {"line": 109, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login exception", "status": "passed", "title": "should handle login exception", "duration": 0.9004919999997583, "failureMessages": [], "location": {"line": 129, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should register successfully", "status": "passed", "title": "should register successfully", "duration": 2.1926009999997405, "failureMessages": [], "location": {"line": 148, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should handle registration failure", "status": "passed", "title": "should handle registration failure", "duration": 0.9280689999995957, "failureMessages": [], "location": {"line": 173, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Logout Action"], "fullName": "Authentication Store (TEST-001) Logout Action should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 1.9051210000006904, "failureMessages": [], "location": {"line": 192, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 1.8654020000003584, "failureMessages": [], "location": {"line": 214, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should handle token refresh failure", "status": "passed", "title": "should handle token refresh failure", "duration": 0.8109230000000025, "failureMessages": [], "location": {"line": 247, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should request password reset successfully", "status": "passed", "title": "should request password reset successfully", "duration": 0.913550999999643, "failureMessages": [], "location": {"line": 259, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 0.9384209999998347, "failureMessages": [], "location": {"line": 273, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should persist auth state to localStorage", "status": "passed", "title": "should persist auth state to localStorage", "duration": 0.783183999999892, "failureMessages": [], "location": {"line": 293, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should restore auth state from localStorage", "status": "passed", "title": "should restore auth state from localStorage", "duration": 0.886091999999735, "failureMessages": [], "location": {"line": 307, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should clear auth state", "status": "passed", "title": "should clear auth state", "duration": 0.857097000000067, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Initialization"], "fullName": "Authentication Store (TEST-001) Initialization should initialize auth state", "status": "passed", "title": "should initialize auth state", "duration": 1.0550599999996848, "failureMessages": [], "location": {"line": 342, "column": 7}, "meta": {}}], "startTime": 1753905132138, "endTime": 1753905132168.0552, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js"}, {"assertionResults": [{"ancestorTitles": ["Tasks Store"], "fullName": "Tasks Store should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 6.832374000000527, "failureMessages": [], "location": {"line": 27, "column": 5}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should add a task successfully", "status": "passed", "title": "should add a task successfully", "duration": 15.46516100000008, "failureMessages": [], "location": {"line": 40, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should handle errors when adding a task", "status": "passed", "title": "should handle errors when adding a task", "duration": 4.284480000000258, "failureMessages": [], "location": {"line": 65, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should generate a task_id if not provided", "status": "passed", "title": "should generate a task_id if not provided", "duration": 4.455348000000413, "failureMessages": [], "location": {"line": 88, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should use provided task_id if valid", "status": "passed", "title": "should use provided task_id if valid", "duration": 1.6236939999998867, "failureMessages": [], "location": {"line": 108, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should throw error for invalid task_id format", "status": "passed", "title": "should throw error for invalid task_id format", "duration": 4.076912000000448, "failureMessages": [], "location": {"line": 130, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "addTask"], "fullName": "Tasks Store addTask should throw error if task_id already exists", "status": "passed", "title": "should throw error if task_id already exists", "duration": 0.9506769999998141, "failureMessages": [], "location": {"line": 148, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should fetch tasks successfully and populate the store", "status": "passed", "title": "should fetch tasks successfully and populate the store", "duration": 2.207851000000119, "failureMessages": [], "location": {"line": 171, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should set loading to true while fetching", "status": "passed", "title": "should set loading to true while fetching", "duration": 17.573300000000017, "failureMessages": [], "location": {"line": 192, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should handle errors when fetching tasks", "status": "passed", "title": "should handle errors when fetching tasks", "duration": 4.189480000000003, "failureMessages": [], "location": {"line": 205, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "fetchTasks"], "fullName": "Tasks Store fetchTasks should pass filters to httpClient.get", "status": "passed", "title": "should pass filters to httpClient.get", "duration": 1.3503520000003846, "failureMessages": [], "location": {"line": 218, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should update a task successfully", "status": "passed", "title": "should update a task successfully", "duration": 1.3941469999999754, "failureMessages": [], "location": {"line": 234, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should handle task not found when updating", "status": "passed", "title": "should handle task not found when updating", "duration": 0.6766910000005737, "failureMessages": [], "location": {"line": 257, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should handle errors from httpClient when updating a task", "status": "passed", "title": "should handle errors from httpClient when updating a task", "duration": 0.9841340000002674, "failureMessages": [], "location": {"line": 270, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "updateTask"], "fullName": "Tasks Store updateTask should update a task successfully using PocketBase ID if task_id not found", "status": "passed", "title": "should update a task successfully using PocketBase ID if task_id not found", "duration": 0.974913000000015, "failureMessages": [], "location": {"line": 292, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should delete a task successfully", "status": "passed", "title": "should delete a task successfully", "duration": 7.552611999999499, "failureMessages": [], "location": {"line": 318, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should handle task not found when deleting", "status": "passed", "title": "should handle task not found when deleting", "duration": 0.8896219999996902, "failureMessages": [], "location": {"line": 338, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should handle errors from httpClient when deleting a task", "status": "passed", "title": "should handle errors from httpClient when deleting a task", "duration": 1.28493299999991, "failureMessages": [], "location": {"line": 350, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "deleteTask"], "fullName": "Tasks Store deleteTask should delete a task successfully using PocketBase ID if task_id not found", "status": "passed", "title": "should delete a task successfully using PocketBase ID if task_id not found", "duration": 0.8655199999993783, "failureMessages": [], "location": {"line": 371, "column": 8}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should get a task by its original ID successfully", "status": "passed", "title": "should get a task by its original ID successfully", "duration": 0.7476689999994051, "failureMessages": [], "location": {"line": 393, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should return null if task is not found by original ID", "status": "passed", "title": "should return null if task is not found by original ID", "duration": 0.5372760000000198, "failureMessages": [], "location": {"line": 411, "column": 7}, "meta": {}}, {"ancestorTitles": ["Tasks Store", "getTaskById"], "fullName": "Tasks Store getTaskById should handle errors from httpClient when getting a task by original ID", "status": "passed", "title": "should handle errors from httpClient when getting a task by original ID", "duration": 0.6178720000007161, "failureMessages": [], "location": {"line": 426, "column": 7}, "meta": {}}], "startTime": 1753905135316, "endTime": 1753905135396.618, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/tasks.spec.js"}, {"assertionResults": [{"ancestorTitles": ["HTTP Client", "HTTPError"], "fullName": "HTTP Client HTTPError should create HTTPError with correct properties", "status": "passed", "title": "should create HTTPError with correct properties", "duration": 3.420447999999851, "failureMessages": [], "location": {"line": 34, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Basic HTTP Methods"], "fullName": "HTTP Client Basic HTTP Methods should make GET request successfully", "status": "passed", "title": "should make GET request successfully", "duration": 5.865429999999833, "failureMessages": [], "location": {"line": 47, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Basic HTTP Methods"], "fullName": "HTTP Client Basic HTTP Methods should make POST request successfully", "status": "passed", "title": "should make POST request successfully", "duration": 1.3617029999995793, "failureMessages": [], "location": {"line": 68, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Basic HTTP Methods"], "fullName": "HTTP Client Basic HTTP Methods should make PUT request successfully", "status": "passed", "title": "should make PUT request successfully", "duration": 1.7970820000000458, "failureMessages": [], "location": {"line": 91, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Basic HTTP Methods"], "fullName": "HTTP Client Basic HTTP Methods should make DELETE request successfully", "status": "passed", "title": "should make DELETE request successfully", "duration": 1.0653569999994943, "failureMessages": [], "location": {"line": 111, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Authentication"], "fullName": "HTTP Client Authentication should add authorization header when token is available", "status": "passed", "title": "should add authorization header when token is available", "duration": 1.2564059999995152, "failureMessages": [], "location": {"line": 131, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Authentication"], "fullName": "HTTP Client Authentication should not add authorization header when no token is available", "status": "passed", "title": "should not add authorization header when no token is available", "duration": 1.2362969999994675, "failureMessages": [], "location": {"line": 155, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "CSRF Token Handling"], "fullName": "HTTP Client CSRF Token Handling should add CSRF token for POST requests", "status": "passed", "title": "should add CSRF token for POST requests", "duration": 1.9714479999993273, "failureMessages": [], "location": {"line": 176, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Erro<PERSON>"], "fullName": "HTTP Client Error Handling should handle 400 validation errors", "status": "passed", "title": "should handle 400 validation errors", "duration": 7.835095999999794, "failureMessages": [], "location": {"line": 212, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Erro<PERSON>"], "fullName": "HTTP Client Error Handling should handle 401 authentication errors", "status": "passed", "title": "should handle 401 authentication errors", "duration": 5.583882999999332, "failureMessages": [], "location": {"line": 239, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Erro<PERSON>"], "fullName": "HTTP Client Error Handling should handle 429 rate limit errors", "status": "passed", "title": "should handle 429 rate limit errors", "duration": 2.38966900000014, "failureMessages": [], "location": {"line": 263, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Erro<PERSON>"], "fullName": "HTTP Client Error Handling should handle 500 server errors", "status": "passed", "title": "should handle 500 server errors", "duration": 6006.8059809999995, "failureMessages": [], "location": {"line": 287, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Erro<PERSON>"], "fullName": "HTTP Client Error Handling should handle network errors", "status": "passed", "title": "should handle network errors", "duration": 1.095988999999463, "failureMessages": [], "location": {"line": 311, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Erro<PERSON>"], "fullName": "HTTP Client E<PERSON><PERSON> Handling should handle timeout errors", "status": "passed", "title": "should handle timeout errors", "duration": 0.8771839999990334, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Token Management"], "fullName": "HTTP Client Token Management should get auth token from localStorage", "status": "passed", "title": "should get auth token from localStorage", "duration": 0.34663800000089395, "failureMessages": [], "location": {"line": 343, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Token Management"], "fullName": "HTTP Client Token Management should return null when no token in localStorage", "status": "passed", "title": "should return null when no token in localStorage", "duration": 0.24619300000085786, "failureMessages": [], "location": {"line": 352, "column": 7}, "meta": {}}, {"ancestorTitles": ["HTTP Client", "Token Management"], "fullName": "HTTP Client Token Management should clear auth state", "status": "passed", "title": "should clear auth state", "duration": 0.5448049999995419, "failureMessages": [], "location": {"line": 360, "column": 7}, "meta": {}}], "startTime": 1753905132127, "endTime": 1753905138171.545, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/utils/__tests__/httpClient.spec.js"}]}
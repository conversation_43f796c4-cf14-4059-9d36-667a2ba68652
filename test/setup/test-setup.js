/**
 * Test Setup Configuration
 * Global test setup for authentication test suite
 */

import { vi } from 'vitest'
import { config } from '@vue/test-utils'
import { createPinia } from 'pinia'

// Set environment variables for testing
process.env.VITE_API_BASE_URL = ''

// Mock browser APIs
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(() => null),
    setItem: vi.fn(() => null),
    removeItem: vi.fn(() => null),
    clear: vi.fn(() => null),
  },
  writable: true,
})

Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: vi.fn(() => null),
    setItem: vi.fn(() => null),
    removeItem: vi.fn(() => null),
    clear: vi.fn(() => null),
  },
  writable: true,
})

// Mock location
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: '',
    assign: vi.fn(),
    replace: vi.fn(),
    reload: vi.fn(),
  },
  writable: true,
})

// Mock fetch
global.fetch = vi.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
  })
)

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
}

// Mock crypto for UUID generation
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: vi.fn(() => 'mock-uuid-1234-5678-9012'),
    getRandomValues: vi.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256)
      }
      return arr
    }),
  },
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  unobserve: vi.fn(),
}))

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock Vue Test Utils global config
config.global.plugins = [createPinia()]

// Mock router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  go: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  currentRoute: {
    value: {
      path: '/',
      name: 'index',
      params: {},
      query: {},
      meta: {},
      fullPath: '/',
    },
  },
}

config.global.mocks = {
  $router: mockRouter,
  $route: mockRouter.currentRoute.value,
}

// Mock Nuxt composables
vi.mock('#app', () => ({
  useRouter: () => mockRouter,
  useRoute: () => mockRouter.currentRoute.value,
  navigateTo: vi.fn(),
  useNuxtApp: () => ({
    $router: mockRouter,
    $route: mockRouter.currentRoute.value,
  }),
}))

// Mock Pinia stores
vi.mock('pinia', async () => {
  const actual = await vi.importActual('pinia')
  return {
    ...actual,
    createPinia: vi.fn(() => actual.createPinia()),
    setActivePinia: vi.fn(actual.setActivePinia),
  }
})

// Test utilities
export const createMockUser = (overrides = {}) => ({
  id: 'user-123',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'user',
  verified: true,
  created: '2023-01-01T00:00:00Z',
  updated: '2023-01-01T00:00:00Z',
  ...overrides,
})

export const createMockAuthResponse = (overrides = {}) => ({
  success: true,
  user: createMockUser(),
  token: 'mock-jwt-token-123',
  ...overrides,
})

export const createMockError = (message = 'Test error', code = 'TEST_ERROR') => ({
  message,
  code,
  status: 400,
})

// Test helpers
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0))

export const flushPromises = () => new Promise(resolve => setTimeout(resolve, 0))

// Mock timers helper
export const mockTimers = () => {
  vi.useFakeTimers()
  return {
    advanceTimersByTime: vi.advanceTimersByTime,
    runAllTimers: vi.runAllTimers,
    runOnlyPendingTimers: vi.runOnlyPendingTimers,
    restore: vi.useRealTimers,
  }
}

// Cleanup function
export const cleanup = () => {
  vi.clearAllMocks()
  vi.clearAllTimers()
  localStorage.clear()
  sessionStorage.clear()
}

// Note: Global test hooks should be defined in individual test files
// This setup file only provides utilities and mocks

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// Export test configuration
export default {
  createMockUser,
  createMockAuthResponse,
  createMockError,
  waitForNextTick,
  flushPromises,
  mockTimers,
  cleanup,
}

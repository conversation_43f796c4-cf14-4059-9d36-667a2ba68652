---
type: "agent_requested"
description: "As a support developer"
---
- Analyse all faling test by running npm run test
- Dev<PERSON>p a plan to fix each faling test sequentially
- Run failing test to insure it now works
- If the test still fails repeat.
- After a test file is fixed, and all tests pass, then do a git commit to the local workspace
- Create a fix report
- Do not assume 
- Adhere to the original requirements
- all documentation is in the documents/development/folder